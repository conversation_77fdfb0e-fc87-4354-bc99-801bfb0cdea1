<template>
  <div :id="id" class="wh100"></div>
</template>

<script>
import { defineComponent, onMounted, watch, ref, nextTick } from "vue";
import * as echarts from "echarts";

export default defineComponent({
  name: "Line",
  components: { echarts },
  props: {
    chartsData: {
      type: Object,
      default: () => {
        return { dataX: [], dataS: [], data: [] };
      },
    },
    dataZoom: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    let id = ref("");
    let myChart;
    watch(
      () => props.chartsData.data,
      (val) => {
        if (val) {
          nextTick(() => {
            renderCharts(props.chartsData);
          });
        }
      },
      { deep: true,immediate:true }
    );
    onMounted(() => {
      id.value = `echarts-${Math.floor(Math.random() * 10000)}`;
      nextTick(() => {
        const el = document.getElementById(id.value);
        if (el) {
          myChart = echarts.init(el);
          renderCharts(props.chartsData);
        }
      });
    });
    function renderCharts({ dataX, dataS }) {
      if (myChart) {
        let option = {
          tooltip: {
            show: true,
            trigger: "axis",
            axisPointer: { type: "shadow" },
          },
          xAxis: {
            type: "category",
            axisLabel: {
              show: true,
              color: "rgba(0, 0, 0, 0.6);",
              fontSize: 13,
              fontStyle: 500,
              // interval: 0,
            },
            axisLine: {
              lineStyle: {
                width: 0,
              },
            },
            boundaryGap: false,
            axisPointer: {
              type: "shadow",
            },
            axisTick: {
              show: false,
            },
            data: dataX,
          },
          yAxis: {
            type: "value",
            axisTick: { show: true },
            axisLabel: {
              show: true,
              color: "rgba(0, 0, 0, 0.6);",
              fontSize: 13,
              fontStyle: 500,
            },
            name: "kW",
            nameTextStyle: {
              color: "rgba(0, 0, 0, 0.6);",
              fontSize: 13,
              fontStyle: 500,
              align: "center", // 单位文本对齐方式
              padding: [0, 0, 0, -30],
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: 0,
                color: "#D2D4DA",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ["#D2D4DA"],
                width: 1,
                type: "dashed",
              },
            },
          },
          grid: {
            left: "1%",
            right: "3%",
            top: "20%",
            bottom: "10%",
            color: "#2B478E",
            containLabel: true,
          },
          series: [
            {
              type: "line",
              data: dataS,
              smooth: true,
              showSymbol: false,
              itemStyle: {
                color: "rgba(237, 66, 66, 1)",
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "rgba(255, 103, 69, 1)" },
                  { offset: 1, color: "rgba(255, 255, 255, 1)" },
                ]),
              },
            },
          ],
          dataZoom: props.dataZoom
            ? [
                {
                  show: true,
                  bottom: 20,
                  realtime: true,
                  height: 10,
                  xAxisIndex: [0],
                  start: 0,
                  end: (3 / dataS?.length) * 100,
                },
                {
                  type: "inside",
                  realtime: false,
                  xAxisIndex: [0],
                  height: 15,
                  start: 0,
                  end: (3 / dataS?.length) * 100,
                },
              ]
            : [],
        };
        myChart.setOption(option, true);
      }
    }
    return { id };
  },
});
</script>


<style lang="less" scoped>
div {
  color: #12ccc6;
}
</style>
