<template>
  <div id="vbar" class="wh100"></div>
</template>

<script setup>
import { nextTick, onMounted,watch } from "vue";
import * as echarts from "echarts";
const props = defineProps(['monthChartsData'])
    watch(
      () => props.monthChartsData.data,
      (val) => {
        if (val) {
           nextTick(() => {
            console.log(val,"VBar")
              let chartDom = document.getElementById("vbar");
              let myChart = echarts.init(chartDom);
              let option = {
                  grid: {
                    left: "1%",
                    right: "3%",
                    top: "20%",
                    bottom: "10%",
                    // 是否包含文本
                    containLabel: true,
                  },
                  xAxis: [
                    {
                      type: "category",
                      data: props.monthChartsData.dataX,
                      axisTick: true,
                      axisLine: {
                        show: false,
                      },
                      axisLabel: {
                        show: true,
                        color: "rgba(0, 0, 0, 0.6);",
                        fontSize: 13,
                        fontStyle: 500,
                      },
                    },
                  ],
                  yAxis: [
                    {
                      // min: 0,
                      // interval: 10,
                      type: "value",
                      axisLine: {
                        show: false,
                      },
                      splitLine: {
                        // 设置 Y 轴的分割线
                        show: true,
                        lineStyle: {
                          color: "#D2D4DA",
                          width: 1,
                          type: "dashed",
                        },
                      },
                      axisLabel: {
                        show: true,
                        color: "rgba(0, 0, 0, 0.6);",
                        fontSize: 13,
                        fontStyle: 500,
                      },
                      name: "元",
                      nameTextStyle: {
                        color: "rgba(0, 0, 0, 0.6);",
                        fontSize: 13,
                        fontStyle: 500,
                        align: "center", // 单位文本对齐方式
                        padding: [0, 0, 0, -30],
                      },
                    },
                  ],
                  series: [
                    {
                      type: "bar",
                      barWidth: 5,
                      data: props.monthChartsData.data,
                      z: 2,
                      itemStyle: {
                        color: "rgba(237, 66, 66, 1)",
                        barBorderRadius: [10, 10, 0, 0],
                      },
                    },
                  ],
                };
              myChart.setOption(option);
            });
        }
      },
      { deep: true,immediate:true }
    );

onMounted(() => {
 
});
</script>