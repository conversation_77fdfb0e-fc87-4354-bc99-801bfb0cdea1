import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/home/<USER>/overview.vue'
import { message } from 'ant-design-vue';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue')
  },
  // {
  //   path: '/viewPowerstation',
  //   name: 'ViewPowerstation',
  //   component: () => import('@/views/viewPowerstation/index.vue')
  // }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

router.beforeEach(async (to, from, next) => {
  let name = sessionStorage.getItem('userName')

  if (to.path !== "/login" && !name) {
    message.info("请登录后使用");
    next("/login");
  } else if (to.path === "/login" && name) {
    // 如果用户已登录且尝试访问登录页面，则重定向到首页
    next("/");
  } else {
    next();
  }
});

export default router
