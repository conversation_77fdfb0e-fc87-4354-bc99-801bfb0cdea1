<template>
  <a-config-provider :locale="zhCN">
    <div id="nav" class="flex-column wh100">
      <div class="top-header w100 flex" v-if="route.path !== '/login'">
          <!-- longi图标 -->
        <div class="system-icon h100 flex-row-end-start">
          <img src="./assets/images/logo.svg" alt="" class="mr16" />
        </div>
         <!-- 头部菜单栏 -->
        <div class="system-info flex1 h100 flex-row-center-end p16 mb16">
          <div class="time pr16">
            {{ renderData.date }} {{ renderData.weekday }}
          
          </div>
          <a-dropdown class="mr16 flex-row-center-start">

            <a class="ant-dropdown-link cursor" @click.prevent>
               <img src="./assets/images/longji_head.webp" alt="" style="height: 44px;width: 44px; margin-right: 10px;" />

              <span>{{ userName }}</span>
              <img src="./assets/images/head_more.png" alt="" style="height: 18px;width: 18px; margin-left: 10px;" />
            </a>

            <!-- 用户名下拉菜单 -->
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a
                    href="javascript:;"
                    @click="() => (passwordModalOpen = true)"
                    >{{ locales.xiugaimima }}</a
                  >
                </a-menu-item>
                <a-menu-item>
                  <a href="javascript:;" @click="() => (apkModalOpen = true)">{{
                    locales.saomiaoxiazai
                  }}</a>
                </a-menu-item>

                 <a-menu-item>
                  <a href="javascript:;" @click="handleLogout">{{
                    locales?.logout 
                  }}</a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
      <div class="main-container flex-row w100" v-if="route.path !== '/login'">
        <div class="menu-info flex-column-start-between h100">
          <!-- 菜单选项栏 -->
          <a-menu
            class="scroll"
            style="overflow-x: hidden"
            v-model:openKeys="state.openKeys"
            v-model:selectedKeys="state.selectedKeys"
            mode="inline"
            :inline-collapsed="state.collapsed"
          >
            <template v-for="item in items">
              <template v-if="!item.children">
    
                <a-menu-item
                  :key="item.key"
                  class="menu-info-item flex-row-center-start p16"
                >
                  <img
                    v-if="state.selectedKeys[0] == item.key"
                    src="@/assets/images/menu_line.png"
                    alt="selected"
                    style="width: 7px; height: 25px; position: absolute; left: 0; margin-right: 0"
                  />
                  <img
                    :src="
                      state.selectedKeys[0] == item.key
                        ? item.icon[1]
                        : item.icon[0]
                    "
                    alt=""
                    style="width: 18px; height: 18px; margin-right: 12px"
                  />
                  <a-tooltip :title="item.label">
                    <span class="ellipsis">{{ item.label }}</span>
                  </a-tooltip>
                </a-menu-item>
              </template>
          
            </template>
            <div
            class="menu-info-item menu-footer flex-column w100"
          >
            <div class="menu-divider"></div>
          </div>
          </a-menu>
     

          <div
            class="menu-info-item menu-footer flex-column w100"
          >
            <div class="slogan-text">
              <p>善用太阳光芒</p>
              <p>创造绿能世界</p>
            </div>
          </div>
        </div>
        <div class="right-sider h100 flex-column">
          <div class="nav-part wh100 scroll">
            <ContentView :path="breadData.labelHerf"></ContentView>
          </div>
        </div>
      </div>
      <Login v-else @loginsuc="onLoginSuc" />
      <!-- 扫描二维码下载APK -->
      <a-modal
        v-model:open="apkModalOpen"
        :title="locales.saomiaoxiazai"
        class="apk-modal"
      >
        <div class="modal-box flex-row-center-center nowrap">
          <img :src="apkImg" alt="" />
        </div>
      </a-modal>
      <!-- 修改密码 -->
      <a-modal
        v-model:open="passwordModalOpen"
        :title="locales.xiugaimima"
        class="password-modal"
      >
        <div class="modal-box flex-row-center-center nowrap">
          <a-form
            ref="formRef"
            :model="formState"
            :label-col="{ span: 7 }"
            :wrapper-col="{ span: 17 }"
          >
            <a-form-item
              :label="locales.xinmima"
              name="newPassword"
              :rules="[
                {
                  required: true,
                  message: locales.xinmima + locales.bunengweikong,
                  trigger: 'blur',
                },
              ]"
            >
              <a-input-password
                v-model:value="formState.newPassword"
                :placeholder="`${locales.qingshuru}${locales.xinmima}`"
              />
            </a-form-item>

            <a-form-item
              :label="locales.querenmima"
              name="confirmPassword"
              :rules="[
                {
                  required: true,
                  message: locales.querenmima + locales.bunengweikong,
                  trigger: 'blur',
                },
              ]"
            >
              <a-input-password
                v-model:value="formState.confirmPassword"
                :placeholder="`${locales.qingshuru}${locales.xinmima}`"
              />
            </a-form-item>

            <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
              <a-button type="primary" class="mt16" @click="changePassword">{{
                locales.queren
              }}</a-button>
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
    </div>
  </a-config-provider>
</template>

<script setup>
import {
  onBeforeMount,
  onUnmounted,
  ref,
  reactive,
  watch,
  computed,
} from "vue";
import moment from "moment";
import { CaretDownOutlined, LogoutOutlined } from "@ant-design/icons-vue";
import ContentView from "./views/ContentView.vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { useRoute, useRouter } from "vue-router";
import Login from "@/views/login/index.vue";
import { message } from "ant-design-vue";
import { updatePassword, logout } from "@/api/list";

const route = useRoute();
const router = useRouter();
const userName = ref(sessionStorage.getItem("userName") || "");
const apkImg =
  "http://***********:38080/photovoltaic-manager/images/download_apk.png";

let apkModalOpen = ref(false);
let passwordModalOpen = ref(false);

let timingWorker = null;
let renderData = ref({});

let locales = ref(JSON.parse(sessionStorage.getItem("locales")) || {});

async function getCurrentTime() {
  renderData.value.systemTime = new Date().getTime();
  timingWorker = setInterval(() => {
    renderData.value.systemTime = new Date().getTime();
    let time = moment(renderData.systemTime).format("YYYY/MM/DD");
    Object.assign(renderData.value, {
      weekday:
        locales.value?.weeks?.[moment(renderData.value.systemTime).day()] || "",
      date: time || "",
    });
  }, 1000);
}

onBeforeMount(async () => {
  getCurrentTime();
});

onUnmounted(() => {
  if (timingWorker) clearInterval(timingWorker);
});

const state = reactive({
  collapsed: false,
  selectedKeys: ["1"],
  openKeys: ["sub1"],
  preOpenKeys: ["sub1"],
});
let items = computed(() => [
  {
    key: "1",
    icon: [
      require("./assets/images/system.png"),
      require("./assets/images/system-active.png"),
    ],
    label: locales.value?.xitongshitu,
    title: "系统概览",
    herf: "/system-view",
  },
  {
    key: "2",
    icon: [
      require("./assets/images/station.png"),
      require("./assets/images/station-active.png"),
    ],
    label: locales.value?.dianzhanliebiao,
    title: "电站列表",
    herf: "/station-list",
  },
  {
    key: "3",
    icon: [
      require("./assets/images/menumap.png"),
      require("./assets/images/menumap-active.png"),
    ],
    label: locales.value?.dianzhanditu,
    title: "电站地图",
    herf: "/station-map",
  },
  {
    key: "4",
    icon: [
      require("./assets/images/equipment.png"),
      require("./assets/images/equipment-active.png"),
    ],
    label: locales.value?.shebeiguanli,
    title: "设备管理",
    herf: "/equipment-management",
  },
  {
    key: "5",
    icon: [
      require("./assets/images/oper.png"),
      require("./assets/images/oper-active.png"),
    ],
    label: locales.value?.jinggoguanli,
    title: "运维信息",
    herf: "/warning",
  },
]);
watch(
  () => state.openKeys,
  (_val, oldVal) => {
    state.preOpenKeys = oldVal;
  }
);
const toggleCollapsed = () => {
  state.collapsed = !state.collapsed;
  state.openKeys = state.collapsed ? [] : state.preOpenKeys;
};

let breadData = computed(() => {
  let i = items.value.findIndex((e) => e.key == state.selectedKeys[0]);
  if (i > -1) {
    return {
      icon: items.value[i].icon[0],
      label: items.value[i].label,
      iconHerf: items.value[i].herf,
      labelHerf: items.value[i].herf,
    };
  } else {
    return {
      icon: "",
      label: "",
      iconHerf: "",
      labelHerf: "",
    };
  }
});

let formRef = ref(null);
let formState = ref({
  newPassword: "",
  confirmPassword: "",
});
async function changePassword() {
  formRef.value
    .validateFields()
    .then(async () => {
      // 校验成功-修改密码
      if (formState.value.newPassword != formState.value.confirmPassword)
        return message.warning(locales.value.mimabuyizhi);
      let res = await updatePassword({
        password: formState.value.newPassword,
        _: new Date().getTime(),
      });
      if (res?.status === 200) {
        message.success(locales.value.caozuochenggong);
        passwordModalOpen.value = false;
      } else {
        message.warning(locales.value.caozuoshibai);
        passwordModalOpen.value = false;
      }
    })
    .catch((errors) => {
      // 校验失败，可以根据需要处理错误信息。
      console.log(errors, "=======");
      return;
    });
}
function onLoginSuc(localeObj, username) {
  locales.value = localeObj;
  userName.value = username;
}
async function handleLogout() {
  let res = await logout();
  if (res?.data?.code === 0) {
    sessionStorage.removeItem("userName");
    sessionStorage.removeItem("locales");
    userName.value = ""; // 清空
    router.replace("/login");
  } else return message.warning(res?.data?.rec || locales.value.caozuoshibai);
}
</script>

<style lang="less">

#app {
  // font-family: Avenir, Helvetica, Arial, sans-serif;
  // -webkit-font-smoothing: antialiased;
  // -moz-osx-font-smoothing: grayscale;
  // text-align: center;
  // color: #2c3e50;
  width: 100%;
  height: 100%;
  min-width: 1280px;
  overflow-x: auto;
}

#nav {
  background-color: #ffffff;
  .top-header {
    height: 60px;
    .system-icon {
      width: 180px;
      // padding-bottom: 20px;
      background-color: #ffffff;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.125),
        0px 12px 16px rgba(13, 13, 18, 0.08);
      justify-content: center;
    }
    .system-info {
      background-color: #fff;
      box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
      .time {
        width: fit-content;
        white-space: nowrap;
      }
    }
  }
  .main-container {
    height: calc(100% - 60px);
    .menu-info {
      padding: 10px 0px 20px 0px;
      background-color: #ffffff;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.125),
        0px 12px 16px rgba(13, 13, 18, 0.08);
      .menu-info-item {
        height: 44px;
        // padding: 0 12px;
        cursor: pointer;
        img {
          width: 18px;
          height: 18px;
        }
        
        &.menu-footer {
          height: auto;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-top: 0;
          cursor: default;
        }
        
        .menu-divider {
          width: 100%;
          height: 1px;
          background-color: #e8e8e8;
          margin: 50px 0;
        }
        
        .slogan-text {
          width: 100%;
          text-align: center;
          color: #c0c0c0;
          font-size: 15px;
          margin-top: 10px;
          margin-bottom: 50px;
          
          p {
            margin: 5px 0;
          }
        }
      }
      .ant-menu {
        width: 154px;
        background-color: #fff;
        border-inline-end: unset;
        .ant-menu-item {
          padding: 0 12px !important;
          .ant-menu-title-content {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #818898;
          }
          &.ant-menu-item-selected {
            // background: #ffffff;
            // border: 1px solid #dfe1e7;
            .ant-menu-title-content {
              color: #E60012;
            }
          }
        }
        .ant-menu-submenu {
          .ant-menu-submenu-title {
            padding: 0 12px !important;
            .ant-menu-title-content {
              margin-left: 0;
              color: #818898;
            }
            .ant-menu-submenu-arrow {
              color: #0d0c12;
            }
          }
          &.ant-menu-submenu-selected {
            .ant-menu-submenu-title {
              .ant-menu-title-content {
                color: #E60012;
              }
            }
          }
        }
        &.ant-menu-inline-collapsed {
          width: 50px;
        }
        &.ant-menu-sub {
          .ant-menu-item {
            padding-left: 38px !important;
            &.ant-menu-item-selected {
              .ant-menu-title-content {
                color: #E60012;
              }
            }
          }
        }
      }
    }
    .right-sider {
      width: calc(100% - 180px);
      padding-bottom: 24px;
      .nav-part {
        max-height: 100%;
        overflow-y: auto;
      }
    }
  }
}

.apk-modal {
  width: 380px !important;
  .modal-box {
    padding: 10px 0 0 !important;
  }
  .ant-modal-footer {
    display: none !important;
  }
}

.password-modal {
  width: 600px !important;
  .modal-box {
    width: 500px !important;
    padding: 20px 0 0 !important;
  }
  .ant-form {
    width: 480px;
    .ant-form-item-control-input-content {
      .ant-input-password {
        width: 100%;
        font-size: 14px;
        &::placeholder {
          font-size: 14px;
        }
        > .ant-input {
          padding: 4px 11px;
          font-size: 14px;
          &::placeholder {
            font-size: 14px;
          }
        }
        &.ant-input-affix-wrapper {
          padding: 0;
        }
        .ant-input-suffix {
          display: none;
          > .anticon {
            color: #fff;
          }
        }
      }
      .ant-form-item {
        display: flex !important;
        flex-direction: row !important;
        justify-content: flex-start !important;
        align-items: center !important;
      }
    }
  }
  .ant-modal-footer {
    display: none !important;
  }
}
</style>
