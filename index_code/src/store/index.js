import { createStore } from 'vuex'

export default createStore({
  state: {
    locales: {},
    equipmentViewData: null, // 存储从设备列表传递过来的查看数据
    equipmentManagerParams: null // 存储设备管理器的参数
  },
  mutations: {
    changeLocales(state, data) {
      state.locales = data;
    },
    setEquipmentViewData(state, data) {
      state.equipmentViewData = data;
    },
    setEquipmentManagerParams(state, params) {
      state.equipmentManagerParams = params;
    },
    clearEquipmentData(state) {
      state.equipmentViewData = null;
      state.equipmentManagerParams = null;
    }
  },
  actions: {
  },
  modules: {
  }
})
