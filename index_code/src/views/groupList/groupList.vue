<template>
  <div class="group-list wh100 plr16">
 
    <div class="table-part w100" v-if="!flag.value">
      <Search :searchData="searchData" @handleEvent="handleTableFilter" />
      <div class="btns flex-row-center-between">
        <div class="btn-left flex-row-center-start nowrap">
          <div
            class="btn"
            @click="() => handleEvent('select-component')"
          >
            <span>{{ locales.xuanzezujian }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('view-component')"
          >
            <span>{{ locales.chakanzujian }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('view-version')"
          >
            <span>{{ locales.chaxunbanben }}</span>
          </div>
          <div
            class="btn ml16" style="display: flex;justify-content: center;align-items: center;"
            @click="() => handleEvent('import-file')"
          >
            <span>{{ locales.daoru }}</span>
          </div>
        </div>
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('add')">
            {{ locales.xinzengshebei }}
          </button>
          <button
            class="btn btn-delete ml16"
            @click="() => handleEvent('delete')"
          >
            {{ locales.shanchu }}
          </button>
        </div>
      </div>
      <Table
        :refreshTableData="() => handleQuery(queryData)"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      >
        <template #action="{ record }">
          <div class="action-buttons">
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('view', record)">
              {{ locales.chakan }}
            </span>
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('update', record)">
              {{ locales.xiugai }}
            </span>
            <span class="action-btn action-btn-danger" @click="() => handleRowEvent('delete', record)">
              {{ locales.shanchu }}
            </span>
          </div>
        </template>
      </Table>
    </div>
    <DeepTable
      v-else-if="flag.value == 'deep'"
      :btns="btns"
      :api="curApi"
      :params="params"
      :refresh="refresh"
      :columns="deepColumn"
      @handleBtnClick="handleBtnClick"
    />
    <Form
      v-else
      :isEdit="flag.value == 'add' || flag.value == 'update'"
      :formData="flag.value == 'view' ? viewData : formData"
      @handleEvent="handleFormEvent"
    ></Form>
    <a-modal
      v-model:open="openVersion"
      :title="locales.chaxunbanben"
      class="group-modal version"
    >
      <div class="modal-box flex-row-center-center nowrap">
        <button
          class="btn flex-row-center-center"
          @click="() => handleVersionQuery(2)"
        >
          查询所属组件版本
        </button>
      </div>
    </a-modal>
    <a-modal
      v-model:open="openImport"
      :title="locales.daorunibianqi"
      :okText="locales.tijiao"
      class="group-modal import"
      @ok="upload"
    >
      <div class="modal-box flex-row-center-start nowrap">
        <span>{{ locales.shangchuanwenjian }}：</span>
        <input type="file" @change="handleFileUpload" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const { groupColumn, viewSelectComponentColumn } = require("@/db.js");
import {
  getDataList,
  saveOrUpdate,
  deleteComponentGroup,
  componentGroupUpload,
  versionQueryTask,
  selectComponent,
  selectOrMoveComponent,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";

const Search = defineAsyncComponent(() =>
  import("../components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);
const DeepTable = defineAsyncComponent(() =>
  import("@/views/components/DeepTable/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
const userName = sessionStorage.getItem("userName") || "";
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

onBeforeMount(() => {
  tableData.columns = groupColumn;
  handleQuery();
});

let flag = ref({});
let viewData = ref([]);
const groupTypeOpt = [
  { label: "独立", value: 1 },
  { label: "并联", value: 2 },
];
const searchData = ref([
  {
    label: locales.value.zuchuanname,
    key: "groupName",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.dianzhanmingcheng,
    key: "powerStationName",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.lurushijan,
    key: "createTime",
    value: undefined,
    type: "rangeTime",
  },
]);
let formData = ref([
  {
    label: locales.value.zuchuanname,
    key: "groupName",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.groupType,
    key: "groupType",
    value: undefined,
    type: "select",
    options: groupTypeOpt,
    required: true,
  },
  {
    label: locales.value.zuchuanbiaoshi,
    key: "chipId",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.edinggonglv,
    key: "power",
    value: undefined,
    type: "input-number",
    required: true,
  },
  {
    label: locales.value.chuangjianren,
    key: "createUserName",
    value: userName,
    type: "input",
    disabled: true,
  },
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}
async function handleQuery(data) {
  let res = await getDataList("componentGroup/queryComponentGroupList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    groupName: data?.groupName || "",
    powerStationName: data?.powerStationName || "",
    createTimeBegin: data?.createTime?.[0] || "",
    createTimeEnd: data?.createTime?.[1] || "",
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource =
        reModel.data?.map((e) => {
          return {
            ...e,
            groupType:
              groupTypeOpt.find((v) => v.value == e.groupType)?.label || "",
          };
        }) || [];
    }
  }
}

async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增or修改
    data.createUserName = undefined;
    if (flag.value.value == "update") data.id = tableData.selectedRowKeys[0];

    let res = await saveOrUpdate("componentGroup", data);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

// 处理行操作事件
async function handleRowEvent(type, record) {
  // 设置选中的行
  tableData.selectedRowKeys = [record.id];
  // 调用原有的事件处理函数
  await handleEvent(type);
}

async function handleEvent(type) {
  if (type == "delete") {
    if (!tableData.selectedRowKeys?.length)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  } else if (type != "add" && type != "import-file") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }

  if (type == "add") {
    // 新增
    flag.value = { label: locales.value.xinzeng, value: "add" };
    formData.value.forEach((v) => {
      if (!v.disabled) v.value = undefined;
    });
  } else if (type == "update") {
    // 修改
    flag.value = { label: locales.value.xiugai, value: "update" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    formData.value.forEach((v) => {
      v.value = curData[v.key];
    });
  } else if (type == "view") {
    // 查看
    flag.value = { label: locales.value.chakan, value: "view" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );

    viewData.value =
      formData.value.map((v) => {
        v.value = curData[v.key];
        let { required, ...others } = v;
        return {
          ...others,
          disabled: true,
        };
      }) || [];

    let arr = [
      {
        label: locales.value.nibianqi,
        key: "inverterName",
        value: curData.inverterName,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.chaungjianshijian,
        key: "createTimeCh",
        value: curData.createTimeCh,
        type: "input",
        disabled: true,
      },
    ];
    viewData.value = [...viewData.value, ...arr];
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deleteComponentGroup({
          id: tableData.selectedRowKeys.join(","),
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "select-component") {
    // 选择组件
    flag.value = { label: locales.value.xuanzezujian, value: "deep" };

    curApi.value = selectComponent;
    params.value = {
      belongsGroupFlag: "selectComponent",
      belongsGroupId: tableData.selectedRowKeys[0],
    };
    deepColumn.value = viewSelectComponentColumn;
    btns.value = [
      { label: locales.value.queren, value: "confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "view-component") {
    // 查看组件
    flag.value = { label: locales.value.chakanzujian, value: "deep" };

    curApi.value = selectComponent;
    params.value = {
      belongsGroupFlag: "detailComponent",
      belongsGroupId: tableData.selectedRowKeys[0],
    };
    deepColumn.value = viewSelectComponentColumn;
    btns.value = [
      { label: locales.value.yichu, value: "remove" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "view-version") {
    // 查询版本
    openVersion.value = true;
    flag.value = {};
  } else if (type == "import-file") {
    // 导入
    openImport.value = true;
    flag.value = {};
  }
}

let btns = ref([]);
let curApi = ref(null);
let params = ref({});
let deepColumn = ref([]);
let refresh = ref(false);
function handleBtnClick({ type, value }) {
  if (refresh.value) refresh.value = false;
  if (type == "back") flag.value = {};
  else {
    if (value?.length) {
      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = null;
          if (type == "confirm") {
            res = await selectOrMoveComponent({
              belongsGroupId: tableData.selectedRowKeys[0],
              id: value.join(","),
              belongsGroupFlag: "selectComponent",
            });
          } else if (type == "remove") {
            res = await selectOrMoveComponent({
              id: value.join(","),
              belongsGroupFlag: "detailComponent",
            });
          }
          if (res.data.code === 0) {
            message.success(locales.value.caozuochenggong);
            refresh.value = true;
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    } else message.info(locales.value.zhengquexuanzecaozuoxiang);
  }
}

let openVersion = ref(false);
let openImport = ref(false);
async function handleVersionQuery(queryType) {
  let res = await versionQueryTask({
    queryType,
    id: tableData.selectedRowKeys[0],
  });
  if (res?.data?.code === 0) {
    message.success(locales.value.caozuochenggong);
    handleQuery();
  } else message.warning(locales.value.caozuoshibai);
  openVersion.value = false;
}
let selectedFile = ref(null);
function handleFileUpload(event) {
  selectedFile.value = event.target.files[0];
  if (selectedFile.value) {
    let nameArr = selectedFile.value.name.split(".") || [];
    let type = nameArr[nameArr.length - 1];
    const typeList = ["xls", "xlsx", "csv"];
    if (typeList.findIndex((e) => e == type) < 0) {
      return message.warning(locales.value.excelgeshi);
    }
  }
}
async function upload() {
  // 提交
  try {
    const formData = new FormData();
    formData.append("upfile", selectedFile.value);
    await componentGroupUpload(formData);
    message.success(locales.value.caozuochenggong);

    openImport.value = false;
  } catch (error) {
    message.warning(locales.value.caozuoshibai);
    openImport.value = false;
  }
}
</script>

<style lang="less" scoped>
.group-list {
  .btns {
    margin: 20px 0 12px;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;

    .action-btn {
      cursor: pointer;
      font-size: 14px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &.action-btn-primary {
        color: #1890ff;

        &:hover {
          color: #40a9ff;
          background-color: #e6f7ff;
        }
      }

      &.action-btn-danger {
        color: #ff4d4f;

        &:hover {
          color: #ff7875;
          background-color: #fff2f0;
        }
      }
    }
  }
}
</style>

<style lang="less">
.group-modal {
  width: 450px !important;
  .modal-box {
    padding: 20px;
    .btn {
      width: 140px !important;
      border-radius: 2px !important;
    }
  }
  &.version {
    .ant-modal-footer {
      .ant-btn-primary {
        display: none !important;
      }
    }
  }
}
</style>