<template>
    <div class="viewPowerstation">
         <div class="card-container">
    <a-tabs v-model:activeKey="activeKey" type="card">
      <a-tab-pane key="1" tab="电站概览">
         <!-- 概览 -->
        <OverView :viewPowerstationValue="viewPowerstationValue"></OverView>
      </a-tab-pane>
      <a-tab-pane key="2" tab="物理视图">
        <physicalView :viewPowerstationValue="viewPowerstationValue"/>
      </a-tab-pane>
      <!-- <a-tab-pane key="3" tab="逻辑视图">
        <div class="preview">
  
            <iframe
            :src="
                logicSrc + '/model/#/preview?id=' + viewPowerstationValue.id || ''
            "
            frameborder="0"
            class="w100 flex1"
            ></iframe>
        </div>
      </a-tab-pane> -->
    </a-tabs>
  </div>
    </div>
</template>
<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";

let emit = defineEmits(["backHome"]);
// 定义 props
const props = defineProps({
  viewPowerstationValue: {
    type: String,
    default: () => null
  },
  active: {
    type: String,
    default: () => null
  },
});
const OverView = defineAsyncComponent(() =>
  import("./overview.vue")
);
const physicalView = defineAsyncComponent(() =>
  import("./physicalView.vue")
);
const activeKey = ref(props.active)
const BASE_URL =
  process.env.NODE_ENV === "production"
    ? window.location.host
    : "***********:39090";
const networkType = window.location.protocol;
const logicSrc = networkType + "//" + BASE_URL;
const back = () => {
   emit("backHome", 1);
}


onBeforeMount(async () => {
    console.log(props.viewPowerstationValue,"props")
});
</script>

<style lang="less" scoped>

:deep(.ant-tabs-nav .ant-tabs-tab){
    font-size: 16px;
    border-radius: 10px 10px, 0px, 0px;
    background: rgba(166, 166, 166, 1);
         color: #fff;
}
:deep(.ant-tabs-nav .ant-tabs-tab-active){
       background: #fff;
}
:deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn){
    font-size: 16px;
    color: rgba(230, 0, 18, 1);


}
.viewPowerstation {
    padding: 32px 60px;
    width: 100%;
    height: 100%;
    .title{
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        .line{
            display: block;
            width:14px;
            height: 0px;
            transform: rotate(90deg);
            border: 2px solid rgba(230, 0, 18, 1);
            border-radius: 5px;
        }
        .text{
            font-size: 15.33px;
            font-weight: 400;
            line-height: 22.2px;
            color: rgba(56, 56, 56, 1);
        }
    }
    .card-container{
        // margin-top: 20px;

    }
}
.preview{
    width: 100%;
    height: 100vh;
}
.w100{
    width: 100%;
    height: 100%;
}
.back{
    position: fixed;
    right: 5%;
}
</style>