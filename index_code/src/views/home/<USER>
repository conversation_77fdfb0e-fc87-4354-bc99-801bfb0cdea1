<template>
  <div class="home">
    <div v-if="home_flag == 1">
    <!-- <div class="icons flex-column-start-between" v-if="path.length < 2">
      <div
        :class="curKey == item.key ? 'active' : ''"
        v-for="item in options"
        :key="item.key"
        @click="() => (curKey = item.key)"
      >
        <img :src="curKey == item.key ? item.icons[1] : item.icons[0]" alt="" />
      </div>
    </div> -->
    <div
      class="station-info"
      v-if="path.length < 2 && curKey != 2"
    >
    <div class="info_item item_1">
      <div class="title">
        <span class="line"></span>
        <span class="text">{{ locales?.stationStatusS }}</span>
      </div>
      <div class="content">
        <div class="content_item line">
          <img src="../../assets/images/index/icon_01.png" alt="">
          <div class="text">
            <div class="tip">{{ locales?.total }}</div>
            <div class="num">{{ 
            stationCount.normalCount +
            stationCount.abnormalCount +
            stationCount.offlineCount 
             }}</div>
          </div>
        </div>
        <div class="content_item">
          <img src="../../assets/images/index/icon_02.png" alt="">
          <div class="text">
            <div class="tip">{{ locales?.normal }}</div>
            <div class="num">{{ stationCount.normalCount || 0 }}</div>
          </div>
        </div>
        <div class="content_item">
          <img src="../../assets/images/index/icon_03.png" alt="">
          <div class="text">
            <div class="tip">{{ locales?.malfunction }}</div>
            <div class="num">{{ stationCount.abnormalCount || 0 }}</div>
          </div>
        </div>
        <div class="content_item">
          <img src="../../assets/images/index/icon_04.png" alt="">
          <div class="text">
            <div class="tip">{{ locales?.offline }}</div>
            <div class="num">{{ stationCount.offlineCount || 0 }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="info_item item_2">
      <div class="title">
        <span class="line"></span>
        <span class="text">{{ locales?.maintenanceStatistics }}</span>
      </div>
      <div class="content">
        <div class="content_item line">
          <img src="../../assets/images/index/icon_05.png" alt="">
          <div class="text">
            <div class="tip">{{ locales?.total }}</div>
            <div class="num">{{ warnCount || 0 }}</div>
          </div>
        </div>
        <div class="content_item line">
          <img src="../../assets/images/index/icon_06.png" alt="">
          <div class="text">
            <div class="tip">{{ locales?.processed }}</div>
            <div class="num">0</div>
          </div>
        </div>
        <div class="content_item line">
          <img src="../../assets/images/index/icon_07.png" alt="">
          <div class="text">
            <div class="tip">{{ locales?.tobeProcessed }}</div>
            <div class="num">{{ warnCount || 0 }}</div>
          </div>
        </div>
        <div class="content_item">
          <img src="../../assets/images/index/icon_10.png" alt="">
          <div class="text">
            <div class="tip">{{ locales?.level1Warning }}</div>
            <div class="num">0</div>
          </div>
        </div>
      </div>
    </div>
    <div class="info_item item_3">
      <div class="address">
        <div class="left">
          <!-- <a-icon type="global" /> -->
          <span>{{ locales?.Jiaxing }}</span>
        </div>
        <div class="right" style="font-size: 14px;">{{ currentDate }}</div>
      </div>
      <div class="address_inner">
        <div class="left">
          <img src="../../assets/images/index/icon_08.png" alt="">
          <div class="text">多云10~21℃</div>
        </div>
        <div class="right">
          <!-- <p>{{ locales?.airQuality }}</p>
          <p>{{ locales?.humidityOfAir }}</p>
          <p>{{ locales?.dailyTemperature }}</p>
          <p>{{ locales?.dailyHumidity }}</p> -->
        </div>
      </div>
    </div>
    </div>


    <div class="table">
      <div class="table_item left">
        <div class="header">
           <div class="title">
            <span class="line"></span>
            <span class="text">电站列表</span>
          </div>
          <div class="more">
            更多
          </div>
        </div>
        <div class="table_inner">
              <!-- 表格 -->
              <div
                class="table-container w100"
                v-if="path.length < 2 && curKey == 1"
              >
                <div class="table-part wh100">
                  <!-- <Search :searchData="searchData" @handleEvent="handleTableFilter" /> -->
                  <!-- <div class="btns flex-row-center-start">
                    <button class="mr16" @click="() => handleViewLink('/viewPowerstation')">
                      概览
                    </button>
                    <button class="mr16" @click="() => handleViewLink('/physical-view')">
                     物理视图
                    </button>
                    <button @click="() => handleViewLink('/logic-view')">
                      逻辑视图
                      {{ locales.luojishitu }}
                    </button>
                  </div> -->
                  <Table
                    class="flex1"
                    :refreshTableData="() => handleQuery(queryData)"
                    :isLoading="tableData.isLoading"
                    :columns="tableData.columns"
                    :dataSource="tableData.dataSource"
                    :paginationFlag="false"
                    :selectedRowKeys="tableData.selectedRowKeys"
                    @pageChange="handlePageChange"
                    @emitRowCheckboxChange="handleRowCheckboxChange"
                  />
                </div>
              </div>
        </div>
      </div>
      <div class="table_item right">
          <div class="header">
            <div class="title">
              <span class="line"></span>
              <span class="text">电站排行榜</span>
            </div>
            <div class="select">
              <a-select
                v-model:value="selectKwhValue"
              >
                <a-select-option
                  v-for="(item,index) in selectKwh"
                  :value="item.value"
                  :key="index"
                  >{{ item.label }}
                </a-select-option>
              </a-select>
            </div>
          </div>
          <div class="date">
             <a-date-picker v-model:value="kwhDate" />
          </div>
          <div class="list">
            <div class="list_item" v-for="(item,index) in kwhData" :key="index">
              <div class="top">
                <img v-if="item.src" :src="item.src" alt="" class="l_icon">
                <span class="text">{{ item.powerStationName }}</span>
              </div>
              <div class="bottom_inner">
                <div class="bottom" :style="{width:`${item.per}%`}"></div>
                <div class="num">{{ item.kwh }}kwh</div>
              </div>
            </div>
          </div>
      </div>
    </div>

    <div class="table_b">
      <div class="table_item left">
          <div class="header">
           <div class="title">
            <span class="line"></span>
            <span class="text">运维信息</span>
          </div>
          <div class="more">
            更多
          </div>
      
        </div>
                <div class="table_inner">
              <!-- 表格 -->
              <div
                class="table-container w100"
                v-if="path.length < 2 && curKey == 1"
              >
                <div class="table-part wh100">
              
                  <Table
                    class="flex1"
                    :refreshTableData="() => handleWarnQuery(queryData)"
                    :isLoading="tableDataWarn.isLoading"
                    :columns="tableDataWarn.columns"
                    :dataSource="tableDataWarn.dataSource"
                    :paginationFlag="false"
                    :selectedRowKeys="tableDataWarn.selectedRowKeys"
                    @pageChange="handlePageChangeWarn"
                    @emitRowCheckboxChange="handleRowCheckboxChangeWarn"
                  />
                </div>
              </div>
        </div>
      </div>
      <div class="table_item right">
          <div class="header">
            <div class="title">
              <span class="line"></span>
              <span class="text">{{ locales?.shehuigongxian }}</span>
              <span class="info"><img src="../../assets/images/index/info.png" alt=""></span>
            </div>
          </div>
          <div class="list">
              <div class="list_item blue">
                <div class="content">
                  <div class="title">
                    <span class="num">29.36</span>
                    <span class="unit">T</span>
                  </div>
                  <div class="text">减排二氧化碳</div>
                </div>
                <img src="../../assets/images/index/report_co2.png" alt="" class="image">
              </div>
              <div class="list_item org">
                 <div class="content">
                  <div class="title">
                    <span class="num">164.6</span>
                    <span class="unit">T</span>
                  </div>
                  <div class="text">节约标准煤总量</div>
                </div>
                  <img src="../../assets/images/index/report_coal.png" alt="" class="image">
              </div>
              <div class="list_item green">
                 <div class="content">
                  <div class="title">
                    <span class="num">520.4</span>
                    <span class="unit">棵</span>
                  </div>
                  <div class="text">等效植树</div>
                </div>
                  <img src="../../assets/images/index/report_tree.png" alt="" class="image">
              </div>
          </div>
      </div>
    </div>
<!-- <div
      class="station-listG"
      v-if="path.length < 2 && curKey != 2"
    > 
    <div class="bottom1 w100 mt16">         
        <div class="title1">
        <span class="line"></span>
        <span class="text1">{{ locales?.shehuigongxian }}</span>
        </div>
        <div class="cards1 w100 flex-row-start-between">
           <img src="../../assets/images/index/report_co2.png" alt="">
          <div class="card-item1 flex1 flex-column-start-center"
              style="margin-right: 24px"
          >
            <div class="flex-row-end-start">
                <div class="value">29.36</div>
                <div class="unit">T</div>
            </div>
            <div class="label">减排CO2总量</div>
          </div>
          <div class="card-item2 flex1 flex-column-start-center">
             <img src="../../assets/images/index/report_coal.png" alt="">
            <div class="flex-row-end-start">
                <div class="value">49.36</div>
                <div class="unit">T</div>
            </div>
            <div class="label">节约标准煤总量</div>
          </div>
          <div class="card-item3 flex1 flex-column-start-center">
             <img src="../../assets/images/index/report_tree.png" alt="">
            <div class="flex-row-end-start">
                <div class="value">520</div>
                <div class="unit">颗</div>
            </div>
            <div class="label">等效植树</div>
            
          </div>
        </div>
        </div>
  </div> -->

    <div
      class="charts-info plr16 w100 flex-row-center-between"
      v-if="path.length < 2 && curKey == 3"
    >
      <div class="left flex1" style="height: 452px">
        <Line :chartsData="chartsData" />
      </div>
      <div class="right h100 flex1 ml16 flex-column-start-between">
        <div class="top w100 flex-column">
          <div class="title flex-row-start-between nowrap">
            {{ locales.dangridianzhanpaiming
            }}<span
              class="nowrap cursor"
              v-if="barData.length > 3"
              @click="() => (openWBarModal = true)"
              >{{ locales.gengduo }}</span
            >
          </div>
          <!-- 横向柱状图 WBar -->
          <div class="box w100 mt16" style="height: calc(100% - 44px)">
            <WBar :legend="legend.slice(0, 3)" :data="barData.slice(0, 3)" />
          </div>
        </div>
        <!-- <div class="bottom w100 mt16">
          <div class="title">{{ locales.shehuigongxian }}</div>
          <div class="cards w100 flex-row-start-between">
            <div
              class="card-item flex1 flex-column-start-center"
              style="margin-right: 24px"
            >
              <div class="flex-row-end-start">
                <div class="value">29.36</div>
                <div class="unit">T</div>
              </div>
              <div class="label">减排CO2总量</div>
            </div>
            <div class="card-item flex1 flex-column-start-center">
              <div class="flex-row-end-start">
                <div class="value">49.36</div>
                <div class="unit">T</div>
              </div>
              <div class="label">节约标准煤总量</div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
  
    <!-- 首页地图 -->
    <Map v-if="path.length < 2 && curKey == 2" />

 
    <!-- 物理视图 -->
    <iframe
      :src="
        logicSrc +
        '/model/#/logic?id=' +
        (tableData.selectedRowKeys[0] || '') +
        '&name=' +
        (tableData.dataSource.find((e) => e.id == tableData.selectedRowKeys[0])
          ?.systemName || '')
      "
      frameborder="0"
      class="w100 flex1"
      v-if="path[1] && path[1] == '/physical-view'"
    ></iframe>

    <!-- 电站电量bar图弹窗 -->
    <a-modal
      v-model:open="openWBarModal"
      :title="locales.dangridianzhanpaiming"
      class="station-modal"
    >
      <WBar :legend="legend" :data="barData" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import tableMixin from "@/mixins/table.js";
import { getDataList, queryWarningTypeCount, groupByKwh,queryComponentByStatistics } from "@/api/list.js";
delete require.cache[require.resolve("@/db.js")];
const { stationColumn1,warningColumn } = require("@/db.js");
import Pie from "../../components/Pie.vue";
import WBar from "../../components/WBar.vue";
import Line from "../../components/Line.vue";
import { message } from "ant-design-vue";
import l_01 from "@/assets/images/index/l_01.png"
import l_02 from "@/assets/images/index/l_02.png"
import l_03 from "@/assets/images/index/l_03.png"
const Map = defineAsyncComponent(() => import("../components/Map/index.vue"));


const Search = defineAsyncComponent(() =>
  import("../components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
let { tableData: tableDataWarn, handlePageChange: handlePageChangeWarn, handleRowCheckboxChange: handleRowCheckboxChangeWarn } = tableMixin();const BASE_URL =
  process.env.NODE_ENV === "production"
    ? window.location.host
    : "***********:39090";
const networkType = window.location.protocol;
const logicSrc = networkType + "//" + BASE_URL;

let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);
const home_flag = ref(1)

let path = ref(["/system-view"]);
let curKey = ref(1);
const options = [
  {
    key: 1,
    icons: [
      require("../../assets/images/s1.png"),
      require("../../assets/images/s1-active.png"),
    ],
  },
  {
    key: 2,
    icons: [
      require("../../assets/images/s2.png"),
      require("../../assets/images/s2-active.png"),
    ],
  },
  {
    key: 3,
    icons: [
      require("../../assets/images/s3.png"),
      require("../../assets/images/s3-active.png"),
    ],
  },
];
let kpis = [
  {
    icon: require("../../assets/images/gonglv.png"),
    label: "当前功率",
    value: "7.82",
    unit: "kW",
  },
  {
    icon: require("../../assets/images/ri.png"),
    label: "当日发电量",
    value: "1.82",
    unit: "度",
  },
  {
    icon: require("../../assets/images/shouyi.png"),
    label: "当日收益",
    value: "2.45",
    unit: "元",
  },
  {
    icon: require("../../assets/images/yue.png"),
    label: "当月发电量",
    value: "0.82",
    unit: "万度",
  },
  {
    icon: require("../../assets/images/eding.png"),
    label: "逆变器额定功率",
    value: "32.00",
    unit: "kW",
  },
  {
    icon: require("../../assets/images/nian.png"),
    label: "年度发电量",
    value: "21.52",
    unit: "万度",
  },
];

let chartsData = {
  dataX: [
    "00:00",
    "01:00",
    "02:00",
    "03:00",
    "04:00",
    "05:00",
    "06:00",
    "07:00",
  ],
  dataS: [2, 8, 10, 13, 12, 21, 35, 40],
  data: [
    { time: "00:00", value: 2 },
    { time: "01:00", value: 8 },
  ],
};


async function handleWarnQuery(data) {
  let res = await getDataList("warning/queryWarningListTo.web", {
    pageNo: tableDataWarn.page,
    pageSize: 3,
    systemName: data?.systemName || "",
    warningType: data?.warningType || "",
    warningStatus: data?.warningStatus || "",
    createTimeBegin: data?.createTime?.[0] || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    createTimeEnd: data?.createTime?.[1] || new Date().toISOString().split('T')[0],
  });
  tableDataWarn.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableDataWarn.total = count;
      tableDataWarn.dataSource =
        reModel.data?.map((e) => {
          return {
            ...e,
            warningType:
              warningTypeOpt.find((v) => v.value == e.warningType)?.label || "",
            // warningStatus:
            //   warningStatusOpt.find((v) => v.value == e.warningStatus)?.label ||
            //   "",
            equipmentType:
              equipmentTypeOpt.find((v) => v.value == e.equipmentType)?.label ||
              "",
            createTime: e.createTime ? new Date(e.createTime).toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            }).replace(/\//g, '-') : "",
          };
        }) || [];
    }
  }
}

async function handleQuery(data) {
  let res = await getDataList("powerstation/queryPowerStationList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    systemName: data?.systemName || "",
    createTimeStart: data?.createTime?.[0] || "",
    createTimeEnd: data?.createTime?.[1] || "",
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource = reModel.data;
    }
  }
}

const warningTypeOpt = [
  { label: "温度报警", value: 1 },
  { label: "遮挡报警", value: 2 },
  { label: "超限警报", value: 3 },
  { label: "遮挡解除报警", value: 5 },
];
const warningStatusOpt = [
  { label: "未处理", value: 1 },
  { label: "已处理", value: 2 },
];
const equipmentTypeOpt = [
  { label: "优化器", value: 1 },
  { label: "逆变器 ", value: 2 },
  { label: "组件", value: 3 },
  { label: "采集器", value: 4 },
];


async function getComponentByStatistics() {
   const today = new Date();
  const day = today.getFullYear() + '-' + 
              String(today.getMonth() + 1).padStart(2, '0') + '-' + 
              String(today.getDate()).padStart(2, '0');
      try {
        // data方式传参
        const res = await queryComponentByStatistics(new URLSearchParams({
          powerStationId: tableData.selectedRowKeys[0],
          day: day
        }));
        console.log(res,"res")
        if (res?.data?.code === 0) {
          message.success('接口调用成功');
        } else {
          message.warning('接口调用失败');
        }
      } catch (error) {
        message.error('接口调用出错');
      }
  console.log(res,"queryComponentByStatistics")
}
let openWBarModal = ref(false);

let warnCount = ref(0);
let stationCount = ref({});
async function getWarningTypeCount() {
  let res = await queryWarningTypeCount();
  if (res?.data?.code === 0) {
    const { offlineCount, normalCount, abnormalCount, typeStats } =
      res.data.reModel;
    stationCount.value = { offlineCount, normalCount, abnormalCount };
    let arr =
      typeStats
        ?.filter(
          (e) => e.warningType == 1 || e.warningType == 2 || e.warningType == 6
        )
        ?.map((v) => v.warningCount) || [];
    warnCount.value = arr.reduce((acc, curr) => (acc || 0) + curr, 0) || 0;
  }
}

let legend = ref([]);
let barData = ref([]);
// 当日电站排名（等价发电时）

const selectKwh = ref([
  {
    label:"等价发电",
    value:"0"
  }
]) 
const selectKwhValue = ref("0")
const kwhDate = ref("")
const kwhData = ref([])
async function getGroupByKwh() {
  legend.value = [];
  barData.value = [];
  let res = await groupByKwh();
  if (res?.data?.code === 0 && res.data.reModel?.length) {

    let list = []
    let allKwh = 0
    res.data.reModel
      .sort((a, b) => b.kwh - a.kwh)
      .forEach((e,i) => {
        allKwh += Number(e.kwh)
        let obj = {
          ...e,
          src: i == 0? l_01: i == 1? l_02 :  i == 2? l_03 : '',
          per: (e.kwh / allKwh).toFixed(1) * 100
        }
        list.push(obj)
        legend.value.push(e.powerStationName);
        barData.value.push(e.kwh);
      });
                console.log(list,"list")
      kwhData.value = list

  }
}

onBeforeMount(async () => {
  getWarningTypeCount();
  tableData.columns = stationColumn1;
  tableDataWarn.columns = warningColumn;
  await handleQuery();
  await handleWarnQuery();
  await getComponentByStatistics();
  getGroupByKwh();
});

const searchData = computed(() => [
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.chaungjianshijian,
    key: "createTime",
    value: undefined,
    type: "rangeTime",
  },
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}

// 跳转物理/逻辑视图
function handleViewLink(secondPath) {
  if (tableData.selectedRowKeys.length != 1)
  return message.info(locales.value.zhengquexuanzecaozuoxiang);
  home_flag.value = 2
  if(secondPath == '/viewPowerstation') {
     home_active.value = '1'
  } else if(secondPath == '/physical-view') {
     home_active.value = '2'
  } else {
     home_active.value = '3'
  }
}

const currentDate = computed(() => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
});
</script>

<style lang="less" scoped>
.home {
  position: relative;
  .icons {
    position: absolute;
    top: 38px;
    right: 0;
    width: 52px;
    height: 144px;
    z-index: 1000;

    > div {
      position: relative;
      width: 32px;
      height: 32px;
      cursor: pointer;
      > img {
        width: 32px;
        height: 32px;
      }
      &.active {
        &::before {
          position: absolute;
          top: 10px;
          right: -18px;
          content: "";
          border-right: 8px solid #fff;
          border-top: 6px solid transparent;
          border-bottom: 6px solid transparent;
        }
      }
    }
  }
  .station-info {
    width: 100%;
    height: 160px;
    display: flex;
    justify-content: space-between;
    .info_item{
      height: 100%;
      background: #fff;
      border-radius: 15px;
      background: rgba(255, 255, 255, 100);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
      padding: 20px 30px;
      .title{
        width: 100%;
        height: 27px;
        display: flex;
        align-items: center;
        .line{
          display: inline-block;
          width: 6px;
          height: 24px;
          background: rgba(230, 0, 18, 1);
          border-radius: 20px;
        }
        .text{
          font-size: 18px;
          line-height: 27px;
          color: rgba(56, 56, 56, 1);
          margin-left: 10px;
        }
      }
      .content{
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: space-between;
        .content_item{
          width: 24%;
          height: 100%;
          display: flex;
          margin-top: 14px;
          img{
            width: 40px;
            height: 40px;
            margin-left: 10px;
          }
          .text{
            margin-left: 10px;
            .tip{
              font-size: 16px;
              color: rgba(102, 102, 102, 1);
            }
            .num{
              font-size: 28px;
              font-weight: 700;
              color: rgba(61, 61, 61, 1);
            }
          }
        }
        .line{
          // margin-left: 10px;
          border-right: 1px solid rgba(207, 207, 207, 1);
        }
      }
      .address{
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 600;
        color: rgba(56, 56, 56, 1);
      }
      .address_inner{
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        .left{
          width: 90px;
          height: 70px;
          img{
            width: 46px;
            height: 46px;
            // margin: 0 auto;
            display: block;
          }
          .text{
            font-size: 14px;
            color: rgba(102, 102, 102, 1);
            margin-top: 5px;
            // text-align: center;
          }
        }
        .right{
          font-size: 14px;
          p{
            margin-bottom: 2px;
          }
        }
      }
    }
    .item_1{
      width: 33%;
    }
    .item_2{
      width: 50%;
    }
    .item_3{
      width: 15%;
      border-radius: 15px;
      background: url(https://img.js.design/assets/img/6577d782a9136f57d8081da6.png);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
    }
  }
  .table{
    width: 100%;
    height: 340px;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    margin-top: 28px;
    .table_item{
      height: 100%;
      border-radius: 15px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
      padding: 20px 30px;
      .header{
        display: flex;
        justify-content: space-between;
        .title{
          width: 100%;
          height: 27px;
          display: flex;
          align-items: center;
          .line{
            display: inline-block;
            width: 6px;
            height: 24px;
            background: rgba(230, 0, 18, 1);
            border-radius: 20px;
          }
          .text{
            font-size: 18px;
            line-height: 27px;
            color: rgba(56, 56, 56, 1);
            margin-left: 10px;
          }
        }
        .more{
          width: 66px;
          height: 28px;
          line-height: 28px;
          border-radius: 14px;
          font-size: 16px;
          background: rgba(252, 253, 253, 1);
          border: 0.6px solid rgba(150, 204, 255, 1);
          color: rgba(22, 93, 255, 1);
          text-align: center;
          cursor: pointer;
        }
        .select{
          width: 150px;
          height: 30px;
          .ant-select{
            width: 100%;
          }
        }
      }
      .date{
        width: 100%;
        height: 30px;
        display: flex;
        justify-content: center;
      }
      .table_inner{
        width: 100%;
        height: 240px;
        margin-top: 20px;
        overflow-y: auto;
      }
      .list{
        width: 100%;
        height: 180px;
        margin-top: 40px;
        overflow-y: auto;
        .list_item{
          width: 100%;
          height: 40px;
          margin-bottom: 20px;
          .top{
            display: flex;
            align-items: center;
            .l_icon{
              width: 20px;
              height: 20px;
            }
            .text{
              color: rgba(125, 125, 125, 1);
              margin-left: 10px;
              font-size: 12px;
            }
          }
          .bottom_inner{
            display: flex;
            height: 30px;
            margin-top: 5px;
             .bottom{
              display: flex;
              align-items: center;
              border-radius: 0px 11px 11px 0px;
              background: linear-gradient(270deg, rgba(245, 122, 122, 1) 0%, rgba(255, 177, 82, 0) 100%);
            }
            .num{
              font-size: 14px;
              margin-left: 20px;
              line-height: 30px;
            }
          }
         
        }
      }
    }
    .left{
      width: 55%;
    }
    .right{
      width: 45%;
      margin-left: 24px;
    }
  }
  .table_b{
    width: 100%;
    height: 310px;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    margin-top: 20px;
     .table_item{
      height: 100%;
      border-radius: 15px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
      padding: 20px 30px;
      .header{
        display: flex;
        justify-content: space-between;
        .title{
          width: 100%;
          height: 27px;
          display: flex;
          align-items: center;
          .line{
            display: inline-block;
            width: 6px;
            height: 24px;
            background: rgba(230, 0, 18, 1);
            border-radius: 20px;
          }
          .text{
            font-size: 18px;
            line-height: 27px;
            color: rgba(56, 56, 56, 1);
            margin-left: 10px;
          }
          .info{
            margin-left: 5px;
          }
        }
        .more{
          width: 66px;
          height: 28px;
          line-height: 28px;
          border-radius: 14px;
          font-size: 16px;
          background: rgba(252, 253, 253, 1);
          border: 0.6px solid rgba(150, 204, 255, 1);
          color: rgba(22, 93, 255, 1);
          text-align: center;
          cursor: pointer;
        }
        .select{
          width: 150px;
          height: 30px;
          .ant-select{
            width: 100%;
          }
        }
      }
      .date{
        width: 100%;
        height: 30px;
        display: flex;
        justify-content: center;
      }
      .table_inner{
        width: 100%;
        height: 240px;
        margin-top: 20px;
        overflow-y: auto;
      }
      .list{
        width: 100%;
        margin-top: 40px;
        display: flex;
        justify-content: space-between;
        .list_item{
          width: 30%;
          height: 160px;
          border-radius: 20px;
          position: relative;
          .image{
            position: absolute;
            right: 17px;
            bottom: 15px;
          }
          .content{
            margin: 28px 0 0 23px;
            .title{
              font-weight: 600;
              color: rgba(56, 56, 56, 1);
              .num{
                font-size: 28px;
              }
              .unit{
                font-size: 16px;
                margin-left: 5px;
              }
            }
            .text{
              margin-top: 4px;
              font-size: 12px;
              color: rgba(105, 105, 105, 1);
            }
          }
        }

        .blue{
          background: rgba(230, 248, 255, 0.8);
        }
        .org{
          background: rgba(252, 237, 222, 0.8);
        }
        .green{
          background: rgba(224, 255, 240, 0.8);
        }
      }
    }
    .left{
      width: 55%;
    }
    .right{
      width: 45%;
      margin-left: 24px;
    }
  }
  .station-listG {
    width: 20%;
    height: 160px;
    display: flex;
    // flex-direction:row-reverse;
    justify-content: space-between;
    .bottom1{
      height: 100%;
      // position: fixed;
      // right: 50%;
      // bottom: 50%;
      // background: #fff;
      border-radius: 15px;
      background: rgba(255, 255, 255, 100);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
      padding: 30px 40px;
      .title1{
        width: 100%;
        height: 27px;
        display: flex;
        align-items: center;
        .line{
          display: inline-block;
          width: 8px;
          height: 27px;
          background: rgba(230, 0, 18, 1);
          border-radius: 20px;
        }
        .text1{
          font-size: 24px;
          line-height: 27px;
          color: rgba(56, 56, 56, 1);
          margin-left: 10px;
        }
      }
      .card-item1{
      width: 15%;
      border-radius: 15px;
      background: url(https://img.js.design/assets/img/6577d782a9136f57d8081da6.png);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
    }
     .card-item2{
      width: 30%;
      border-radius: 15px;
      background: url(https://img.js.design/assets/img/6577d782a9136f57d8081da6.png);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
    }
    .card-item3{
      width: 60%;
      border-radius: 15px;
      background: url(https://img.js.design/assets/img/6577d782a9136f57d8081da6.png);
      box-shadow: 6px 6px 54px  rgba(0, 0, 0, 0.05);
    }
    }
   
  }

  .charts-info {
    height: 414px;
    overflow: hidden;
    .right {
      .top,
      .bottom {
        height: 214px;
        background-color: #fff;
        padding: 20px 24px 0;
        border-radius: 2px;
        overflow: hidden;
        .title {
          font-size: 18px;
          font-weight: 500;
          line-height: 28px;
          color: #0d0c12;
        }
        .cards {
          margin-top: 12px;
          .card-item {
            height: 110px;
            padding: 0 24px;
            background-image: url("../../assets/images/bg2.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            .value {
              font-size: 28px;
              color: #0d0c12;
              line-height: 44px;
            }
            .unit {
              font-size: 18px;
              color: #0d0c12;
              margin-left: 5px;
              line-height: 40px;
            }
            .label {
              font-size: 16px;
              color: #a6a6a6;
              margin-top: 5px;
            }
            &:first-child {
              background-image: url("../../assets/images/bg1.png");
              background-size: 100% 100%;
              background-repeat: no-repeat;
            }
          }
        }
      }
      .top {
        flex-shrink: 0;
      }
    }
  }
  .table-container {
    // height: calc(100% - 421px);
    max-height: 100%;
  }
  .table-part {
    height: 100% !important;
    .btns {
      margin: 12px 0;
      > button {
        min-width: 84px;
        height: 32px;
        border-radius: 2px;
        background: #165dff;
        font-size: 14px;
        color: #fff;
        border: none;
        cursor: pointer;
        &:active {
          opacity: 0.5;
        }
      }
    }
  }
}
</style>

<style lang="less">
.station-modal {
  .ant-modal-footer {
    display: none !important;
  }
  .ant-modal-body {
    height: 266px !important;
    padding-top: 16px !important;
  }
}
</style>
