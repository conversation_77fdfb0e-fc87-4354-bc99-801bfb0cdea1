<template>
  <div class="overview w100">
    <div class="left overview_item">
        <div class="title">
            <span class="line"></span>
            <span class="text">电站概览</span>
       </div>
       <div class="base">
         <img src="@/assets/images/index/station1.png" alt="" />
         <div class="content">
          <div class="name">
            <div class="txt">{{ viewPowerstationValue.systemName }}</div>
            <div class="status"> <img src="@/assets/images/index/icon_02.png" alt="" class="icon"/> <span>
              {{ viewPowerstationValue.status == 1?'待调试':'已调试' }}
            </span></div>
          </div>
          <div class="address">
            {{ viewPowerstationValue.provinceName }}·
            {{ viewPowerstationValue.cityName }} 
            {{ viewPowerstationValue.streetName }}
          </div>
          <div class="tags">
            <div class="tag">{{ viewPowerstationValue.power }} kWp</div>
            <div class="tag">{{ viewPowerstationValue.createTimeCh }}</div>
            <div class="tag">
              已运行 <span>{{daysDifference}}</span> 天
            </div>
          </div>
         </div>
         <div class="weather">
          <div class="item">
            <img src="@/assets/images/location.png" alt="">
            <span>{{viewPowerstationValue.cityName}}·{{ weatherData.text }}  {{ weatherData.temp}}℃</span>
          </div>
          <div class="item">
            <img src="@/assets/images/sun.png" alt="">
            <span>日照时间  {{viewPowerstationValue.sunuptime}}~{{viewPowerstationValue.sundowntime}}</span>
          </div>
          <div class="item">
            <img src="@/assets/images/orientation.png" alt="">
            <span>东经{{Number(viewPowerstationValue.longitude).toFixed(2)}}    北纬{{Number(viewPowerstationValue.latitude).toFixed(2)}}</span>
          </div>
         </div>
       </div>
       <div class="data_item">
        <div class="data_card yellow">
          <img src="@/assets/images/index/shoiyi.png" alt="">
          <div class="num">
            <div class="tip">当日收益 (元)</div>
            <div class="num_txt">{{ (powerStationInfo.dayKwh*0.3).toFixed(2) }}</div>
          </div>
        </div>
        <div class="card_list">
          <div class="card_item">
            <div class="title">当月收益 (元)</div>
            <div class="num_txt">{{ (monthKwh*0.3).toFixed(2) }}</div>
          </div>
          <div class="card_line"></div>
           <div class="card_item">
              <div class="title">当年收益 (元)</div>
            <div class="num_txt">{{ (yearKwh*0.3).toFixed(2) }}</div>
           </div>
          <div class="card_line"></div>
           <div class="card_item">
              <div class="title">累计收益 (元)</div>
            <div class="num_txt">{{ (powerStationInfo.kwh*0.3).toFixed(2) }}</div>
           </div>
        </div>
       </div>
       <div class="data_item">
        <div class="data_card green">
          <img src="@/assets/images/index/report_day_power.png" alt="">
          <div class="num">
            <div class="tip">当日发电 (kWh)</div>
            <div class="num_txt">{{ powerStationInfo.dayKwh }}</div>
          </div>
        </div>
        <div class="card_list">
          <div class="card_item">
            <div class="title">当月发电 (kWh)</div>
            <div class="num_txt">{{ monthKwh }}</div>
          </div>
          <div class="card_line"></div>
           <div class="card_item">
              <div class="title">当年发电 (kWh)</div>
            <div class="num_txt">{{ yearKwh }}</div>
           </div>
          <div class="card_line"></div>
           <div class="card_item">
              <div class="title">累计发电 (kWh)</div>
            <div class="num_txt">{{ powerStationInfo.kwh }}</div>
           </div>
        </div>
       </div>
       <div class="left_bottom">
        <div class="header">
            <div class="title">
               <span class="left_line"></span>
              <span class="text">社会贡献</span>
              <span class="info"><img src="@/assets/images/index/info.png" alt=""></span>
            </div>
          </div>
            <div class="list">
              <div class="list_item blue">
                <div class="content">
                  <div class="title">
                    <span class="num">{{ (powerStationInfo.kwh*0.997).toFixed(2) }}</span>
                    <span class="unit">T</span>
                  </div>
                  <div class="text">减排二氧化碳</div>
                </div>
                <img src="@/assets/images/index/report_co2.png" alt="" class="image">
              </div>
              <div class="list_item org">
                 <div class="content">
                  <div class="title">
                    <span class="num">{{ (powerStationInfo.kwh*0.404).toFixed(2) }}</span>
                    <span class="unit">T</span>
                  </div>
                  <div class="text">节约标准煤总量</div>
                </div>
                  <img src="@/assets/images/index/report_coal.png" alt="" class="image">
              </div>
              <div class="list_item green">
                 <div class="content">
                  <div class="title">
                    <span class="num">{{ (powerStationInfo.kwh*0.054).toFixed(2) }}</span>
                    <span class="unit">棵</span>
                  </div>
                  <div class="text">等效植树</div>
                </div>
                  <img src="@/assets/images/index/report_tree.png" alt="" class="image">
              </div>
          </div>
       </div>
    </div>
    <div class="line"></div>
    <div class="right overview_item">
       <div class="title">
            <span class="line"></span>
            <span class="text">发电数据</span>
       </div>
       <div class="right_card">
        <div class="card_item">
          <img src="@/assets/images/index/report_power_now.png" alt="">
          <div class="card_content">
            <div class="title">实时功率 </div>
            <div class="num_txt">{{ powerStationInfo.power }}<span style="color: rgba(117, 117, 117, 1);">kW</span></div>
          </div>
        </div>
        <div class="card_line"></div>
          <div class="card_item">
          <img src="@/assets/images/index/report_power_ratio.png" alt="">
          <div class="card_content">
            <div class="title">功率占比 </div>
            <div class="num_txt">{{ (powerStationInfo.power/viewPowerstationValue.power).toFixed(2) }}<span style="color: rgba(117, 117, 117, 1);">%</span></div>
          </div>
        </div>
        <div class="card_line"></div>
          <div class="card_item">
          <img src="@/assets/images/index/report_power_max .png" alt="">
          <div class="card_content">
            <div class="title">装机功率 </div>
            <div class="num_txt">{{ viewPowerstationValue.power }}<span style="color: rgba(117, 117, 117, 1);">kWp</span></div>
          </div>
        </div>
       </div>
       <div class="date_card">
        <div :class="['date_item',dateFlag == index? 'active':'']" v-for="(item,index) in dateList" :key="index" @click="selectDate(index)">{{ item.label }}</div>
       </div>
        <div class="select_date">
             <div class="select">
               <a-date-picker v-model:value="selectKwhValue" :format="dateFormat"  @change="dateDay" v-if="dateFlag == 0"/>
               <a-date-picker v-model:value="selectMouthValue" picker="month" :format="dateMouthFormat"  @change="dateMouth" v-if="dateFlag == 1"/>
               <a-date-picker v-model:value="selectYearValue" picker="year"  @change="dateYear" v-if="dateFlag == 2"/>
            </div>
        </div>
        <div class="echarts">
          <!-- 日 -->
            <Line :chartsData="chartsData" v-if="dateFlag == 0"/>
          <!-- 月/年/总 -->
           <VBar :monthChartsData="monthChartsData" v-else/>
        </div>
    </div>

  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed, onMounted } from "vue";
import { 
  queryReportByMinute,
  queryReportByDay,
  queryComponentByMonth,
  queryReportByYear,
  queryComponentByStatistics,
  getWeather,
  queryComponentListTo } from "@/api/list.js";
import { message, Modal } from "ant-design-vue";
import moment from 'moment';
import dayjs, { Dayjs } from 'dayjs';
import 'moment/dist/locale/zh-cn';
import HBar from "../../../components/HBar.vue";
import VBar from "../../../components/VBar.vue";
import Line from "../../../components/Line.vue";
const props = defineProps({
  viewPowerstationValue: {
    type: Object,
    default: () => {}
  }
});

const dateList = ref([
  {
    label:"日",
    value:0
  },
  {
    label:"月",
    value:1
  },
  {
    label:"年",
    value:2
  },
  {
    label:"总",
    value:3
  },
])
const dateFlag = ref(0)
const selectDate = (index) => {
  dateFlag.value = index
  if(index == 0) {
    getQueryReportByMinute()
  } else if(index == 1) {
    getQueryReportByDay()
  } else if(index == 2){
    getQueryComponentByMonth()
  } else {
    getQueryReportByYear()
  }
}
const dateFormat = 'YYYY-MM-DD';
const selectKwhValue = ref()
const dateMouthFormat = 'YYYY-MM';
const selectMouthValue = ref()
const dateYearFormat = 'YYYY';
const selectYearValue = ref()
// 日

const chartsData = ref({
  dataX: [],
  dataS: [],
  data: [
    { time: "00:00", value: 2 },
    { time: "01:00", value: 8 },
  ],
});
// 月
const monthChartsData = ref({
   dataX: [],
  data: [],
})
const powerStationInfo = ref({})
const reportByDayInfo = ref({})
const componentByMonthInfo = ref({})

// 运行天数
const daysDifference = ref(0)
// 当月发电
const monthKwh = ref(0)
// 当年发电
const yearKwh = ref(0)
// 天气
const weatherData = ref({})
// 
async function getComponentByStatistics() {
   const today = new Date();
   const day = today.getFullYear() + '-' + 
              String(today.getMonth() + 1).padStart(2, '0') + '-' + 
              String(today.getDate()).padStart(2, '0');
      try {
        // data方式传参
        const res = await queryComponentByStatistics(new URLSearchParams({
          powerStationId: props.viewPowerstationValue.id,
          day: day
        }));
        // console.log(res,"res") 
        if (res?.data?.code === 0) {
          const infoData = res.data.reModel
          // dayKwh 当日发电量 
          // kwh 累计发电量
          // power 当前功率
          // powerSum 今天到现在为止的功率之和
          powerStationInfo.value = infoData




      // 将创建时间字符串转换为Date对象
      const creationDate = new Date(props.viewPowerstationValue.createTimeCh);
      // 获取当前时间
      const currentDate = new Date();
      // 计算两个日期之间的毫秒差
      const diffInMs = currentDate - creationDate;
      // 将毫秒差转换为天数
      const days = diffInMs / (24 * 60 * 60 * 1000);
      // 取整，得到完整的天数差（你也可以使用Math.round()来四舍五入）
      daysDifference.value= Math.floor(days);
        } else {
          message.warning('接口调用失败');
        }
      } catch (error) {
        message.error('接口调用出错');
      }


try {
        // data方式传参
        const restest = await queryComponentListTo(new URLSearchParams({
          powerStationId: props.viewPowerstationValue.id,
          date: day,
          createType: 1 
        }));
        // console.log(res,"res") 
        if (restest?.data?.code === 0) {
          const infoDataTest = restest.data.reModel
          // dayKwh 当日发电量 
          // kwh 累计发电量
          // power 当前功率
          // powerSum 今天到现在为止的功率之和
          // powerStationInfo.value = infoDataTest
        } else {
          message.warning('接口调用失败');
        }
      } catch (error) {
        message.error('接口调用出错');
      }

}
// 日
const dateDay = () => {
  getQueryReportByMinute()
}
async function getQueryReportByMinute() {
   const today =  selectKwhValue.value?new Date(selectKwhValue.value):new Date();
   const day = today.getFullYear() + '-' + 
              String(today.getMonth() + 1).padStart(2, '0') + '-' + 
              String(today.getDate()).padStart(2, '0');
    selectKwhValue.value = dayjs(day,dateFormat)
      try {
        // data方式传参
        const res = await queryReportByMinute(new URLSearchParams({
          powerStationId: props.viewPowerstationValue.id,
          day:day
        }));
        if (res?.data?.code === 0) {
          const infoData = res.data.reModel
          console.log(infoData,"getQueryReportByMinute")
          let num = 0;
          let xData = []
          let yData = []
          let Data = []
          infoData.data.forEach((item) => {
            xData.push((item.createTimeCh).slice(11,16))
            yData.push(item.power)
            Data.push({
             time: item.createTimeCh, value: item.kwh 
            })
            num += item.kwh?Number(item.kwh):0
          })
          chartsData.value.dataX = xData
          chartsData.value.dataS = yData
          chartsData.value.data = Data
        } else {
          message.warning('接口调用失败');
        }
      } catch (error) {
        message.error('接口调用出错');
      }
}
// 月

const dateMouth = () => {
  getQueryReportByDay()
}
async function getQueryReportByDay() {
   const today =  selectMouthValue.value?new Date(selectMouthValue.value):new Date();
   const day = today.getFullYear() + '-' + 
              String(today.getMonth() + 1).padStart(2, '0')
               selectMouthValue.value = dayjs(day,dateMouthFormat)
      try {
        // data方式传参
        const res = await queryReportByDay(new URLSearchParams({
          powerStationId: props.viewPowerstationValue.id,
          month: day
        }));
        if (res?.data?.code === 0) {
          const infoData = res.data.reModel
          console.log(infoData,"getQueryReportByDay")
          let num = 0;
          let xData = []
          let yData = []
          infoData.data.forEach((item) => {
            xData.push(item.createTimeCh)
            yData.push(item.kwh)
            num += item.kwh?Number(item.kwh):0
          })
          monthChartsData.value.dataX = xData
          monthChartsData.value.data = yData

          monthKwh.value = (num + Number(powerStationInfo.value.dayKwh)).toFixed(2)
           console.log(monthKwh.value,"monthKwh.value")
          reportByDayInfo.value = infoData
        } else {
          message.warning('接口调用失败');
        }
      } catch (error) {
        message.error('接口调用出错');
      }
}

// 年

const dateYear = () => {
  getQueryComponentByMonth()
}
async function getQueryComponentByMonth() {
 const today =  selectYearValue.value?new Date(selectYearValue.value):new Date();
  const day = today.getFullYear()
  console.log(day,"day")
        selectYearValue.value = dayjs(day+'',dateYearFormat)
        console.log(selectYearValue.value," selectYearValue.value")
      try {
        // data方式传参
        const res = await queryComponentByMonth(new URLSearchParams({
          powerStationId: props.viewPowerstationValue.id,
          day: day
        }));
        if (res?.data?.code === 0) {
          const infoData = res.data.reModel
            let num = 0;
              let xData = []
          let yData = []
          infoData.data.forEach((item) => {
              xData.push(item.createTimeCh)
            yData.push(item.kwh)
            num += item.kwh?Number(item.kwh):0
          })
          monthChartsData.value.dataX = xData
          monthChartsData.value.data = yData
          yearKwh.value = (num + Number(powerStationInfo.value.dayKwh)).toFixed(2)
             console.log(yearKwh.value,"yearKwh.value")
          componentByMonthInfo.value = infoData
        } else {
          message.warning('接口调用失败');
        }
      } catch (error) {
        message.error('接口调用出错');
      }
  console.log(res,"queryComponentByStatistics")
}
// 总
async function getQueryReportByYear() {
      try {
        // data方式传参
        const res = await queryReportByYear(new URLSearchParams({
          powerStationId: props.viewPowerstationValue.id
        }));
        if (res?.data?.code === 0) {
          const infoData = res.data.reModel
              let xData = []
          let yData = []
           infoData.data.forEach((item) => {
              xData.push(item.createTimeCh)
            yData.push(item.kwh)
          })
          monthChartsData.value.dataX = xData
          monthChartsData.value.data = yData
          console.log(infoData,"queryComponentByMonth")
        
        } else {
          message.warning('接口调用失败');
        }
      } catch (error) {
        message.error('接口调用出错');
      }
  console.log(res,"queryComponentByStatistics")
}

async function getWeatherData() {
      try {
        // data方式传参
        const res = await getWeather(new URLSearchParams({
          districtId: props.viewPowerstationValue.districtId,
          dataType: "now"
        }));
        if (res?.data?.code === 0) {
          const infoData = res.data.reModel
          weatherData.value = infoData
        } else {
          message.warning('接口调用失败');
        }
      } catch (error) {
        message.error('接口调用出错');
      }
  console.log(res,"queryComponentByStatistics")
}


onMounted(async () => {
    console.log(props.viewPowerstationValue,"props111")
    getComponentByStatistics()
    getQueryReportByMinute()
    getQueryReportByDay()
    getWeatherData()
});
</script>

<style lang="less" scoped>
.overview {
  background: #fff;
  height: 730px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px;
  .overview_item{
    width: 49%;
    height: 100%;
    padding: 43px 0px;
     .title{
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        .line{
            display: block;
            width:16px;
            height: 0px;
            transform: rotate(90deg);
            border: 2px solid rgba(230, 0, 18, 1);
            border-radius: 5px;
        }
        .text{
            font-size: 18px;
            font-weight: 400;
            color: rgba(56, 56, 56, 1);
        }
    }
  }
  .line{
    width: 0.5px;
    height: 85%;
    background: rgba(209, 209, 209, 1);
  }
  .left{
    .base{
      width: 100%;
      height: 136px;
      border-radius: 20px;
      background: rgba(245, 245, 245, 1);
      margin-top: 17px;
      padding: 22px 25px;
      display: flex;
      margin-bottom: 11px;
      img{
        width: 93px;
        height: 93px;
      }
      .content{
        width: 45%;
        height: 93px;
        margin-left: 14px;
        .name{
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .txt{
            font-size: 16px;
            color: rgba(56, 56, 56, 1);
          }
          .status{
              font-size: 14px;
              color: #43cf7c;
              line-height: 26px;
              height: 26px;
              border-radius: 14px;
              background: rgba(115, 199, 100, 0.2);
              text-align: center;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 5px;
              .icon{
                width: 24px;
                height: 24px;
              }
          }
        }
        .address{
          font-size: 13.65px;
          color: rgba(128, 128, 128, 1);
          margin-top: 10px;
        }
        .tags{
          width: 100%;
          height: 16px;
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          .tag{
            font-size: 14px;
            color: rgba(128, 128, 128, 1);
            text-align: center;
            span{
              color: rgba(50, 116, 249, 1);
            }
          }
        }
      }
      .weather{
        width: 30%;
        height: 93px;
        margin-left: 42px;
        .item{
          width: 100%;
          height: 22px;
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          img{
            width: 17px;
            height: 17px;

          }
          span{
            font-size: 14px;
            color: rgba(153, 153, 153, 1);
            margin-left: 10px;
          }
        }
      }
    }
    .data_item{
      width: 100%;
      height: 100px;
      margin-top: 26px;
      display: flex;
      align-items: center;
      .data_card{
        width: 220px;
        height: 100%;
        border-radius: 20px;
        padding: 21px;
        display: flex;
        align-items: center;
        img{
          width: 40px;
          height: 40px;
        }
        .num{
          flex: 1;
          margin-left: 15px;
          height: 100%;
          .tip{
            font-size: 16px;
            color: rgba(56, 56, 56, 1);
          }
          .num_txt{
            font-size: 30px;
            color: rgba(18, 18, 18, 1);
            // margin-top: 5px;
          }
        }

      }
      .yellow{
        background: rgba(255, 195, 56, 0.15);
      }
      .green{
        background: rgba(224, 255, 240, 0.8);
      }
      .card_list{
        width: calc(100% - 220px);
        height: 70px;
        display: flex;
        align-items: center;
        margin-left: 11px;
      .card_item{
        width: 33%;
        height: 100%;
         margin-left: 17px;
         .title{
            font-size: 14px;
            color: rgba(117, 117, 117, 1);
          }
          .num_txt{
            font-size: 24px;
            color: rgba(18, 18, 18, 1);
          }
      }
      .card_line{
        width: 1px;
        height: 40px;
        background: rgba(217, 217, 217, 1);;
      }
      }
    }
    .left_bottom{
      margin-top: 44px;
      .header{
          display: flex;
          justify-content: space-between;
          .title{
            width: 100%;
            height: 27px;
            display: flex;
            align-items: center;
            .left_line{
              display: inline-block;
              width: 6px;
              height: 24px;
              background: rgba(230, 0, 18, 1);
              border-radius: 20px;
            }
            .text{
              font-size: 18px;
              line-height: 27px;
              color: rgba(56, 56, 56, 1);
              margin-left: 10px;
            }
            .info{
              margin-left: 5px;
            }
          }
      }
       .list{
        width: 100%;
        margin-top: 40px;
        display: flex;
        justify-content: space-between;
        .list_item{
          width: 30%;
          height: 110px;
          border-radius: 20px;
          position: relative;
          .image{
            width: 51px;
            height: 35px;
            position: absolute;
            right: 17px;
            bottom: 15px;
          }
          .content{
            margin: 28px 0 0 23px;
            .title{
              font-weight: 600;
              color: rgba(56, 56, 56, 1);
              .num{
                font-size: 28px;
              }
              .unit{
                font-size: 16px;
                margin-left: 5px;
              }
            }
            .text{
              margin-top: 4px;
              font-size: 12px;
              color: rgba(105, 105, 105, 1);
            }
          }
        }

        .blue{
          background: rgba(230, 248, 255, 0.8);
        }
        .org{
          background: rgba(252, 237, 222, 0.8);
        }
        .green{
          background: rgba(224, 255, 240, 0.8);
        }
      }
    }
  }
  .right{
    .right_card{
      width: 100%;
      height: 85px;
      border-radius: 20px;
      background: rgba(247, 247, 247, 1);
      margin-top: 17px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .card_item{
        width: 33%;
        height: 100%;
        margin-left: 40px;
         display: flex;
         align-items: center;
         img{
          width: 40px;
          height: 20px;
         }
         .card_content{
          margin-left: 21px;
          .title{
            font-size: 14px;
            color: rgba(117, 117, 117, 1);
          }
          .num_txt{
            font-size: 22px;
            color: rgba(18, 18, 18, 1);
          }
         }

      }
      .card_line{
        width: 1px;
        height: 30px;
        background: rgba(217, 217, 217, 1);;
      }
    }
    .date_card{
      width: 100%;
      height: 46px;
      border-radius: 90px;
      background: rgba(247, 247, 247, 1);
      box-shadow:inset 0px 0px 4.05px  rgba(18, 18, 18, 0.1);
      margin-top: 40px;
      padding: 4px 8px;
      display: flex;
      justify-content: space-between;
      .date_item{
        width: 25%;
        height: 100%;
        border-radius: 90px;
        text-align: center;
        line-height: 40px;
        font-size: 18px;
        color: rgba(128, 128, 128, 1);
        cursor: pointer;
      }
      .active{
        background: #fff;
      }
    }
    .select_date{
      width: 100%;
      height: auto;
      display: flex;
      justify-content: center;
      margin-top: 26px;
       .select{
          width: 150px;
          height: 30px;
          .ant-select{
            width: 100%;
          }
        }
    }
    .echarts{
      width: 100%;
      height: 400px;
    }
  }
}
</style>