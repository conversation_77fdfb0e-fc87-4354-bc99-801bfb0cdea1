<template>
  <div class="overview w100">
    <div class="left overview_item">
        <div class="title">
            <span class="line"></span>
            <span class="text">物理视图</span>
       </div>
       <div class="card">
        <div class="card_item border_r">
          <img src="@/assets/images/index/station_view_count_total.png" alt="">
          <div class="text">
            <span class="label">总计</span>
            <span class="value">{{ allData }}</span>
          </div>
        </div>
        <div class="card_item">
          <img src="@/assets/images/index/station_view_count_normal.png" alt="">
          <div class="text">
            <span class="label">正常</span>
            <span class="value">{{ normalData }}</span>
          </div>
        </div>
        <!-- <div class="card_item">
          <img src="@/assets/images/index/station_view_optimizer.png" alt="">
          <div class="text">
            <span class="label">故障</span>
            <span class="value">04</span>
          </div>
        </div> -->
        <div class="card_item">
          <img src="@/assets/images/index/station_view_count_abnormal.png" alt="">
          <div class="text">
            <span class="label">异常</span>
            <span class="value">{{ abnormalData }}</span>
          </div>
        </div>
        <div class="card_item">
          <img src="@/assets/images/index/station_view_count_offline.png" alt="">
          <div class="text">
            <span class="label">离线</span>
            <span class="value">{{ offLineData }}</span>
          </div>
        </div>
       </div>
       <div class="select_content">
            <div class="select">
              <a-select
                v-model:value="selectKwhValue"
              >
                <a-select-option
                  v-for="(item,index) in selectKwh"
                  :value="item.value"
                  :key="index"
                  >{{ item.label }}
                </a-select-option>
              </a-select>
            </div>
       </div>
       <!-- <div class="wl_icon"></div> -->
    </div>
    <div class="right overview_item">
      <!-- <div class="line">
        <div class="line_top line_item">
          <div class="query">
            <div class="dot"></div>
            <div class="text">
              <span class="line_1">—</span>
              <span class="line_2">/</span>
              <span class="line_3">~</span>
            </div>
          </div>
          <div class="num">01#</div>
        </div>
        <div class="line_bottom line_item">
            <div class="query">
            <div class="dot"></div>
            <div class="text">
              <span class="line_1">—</span>
              <span class="line_2">/</span>
              <span class="line_3">~</span>
            </div>
          </div>
          <div class="num">02#</div>
        </div>
      </div> -->
      <div class="right_card">
        <div class="card_item" v-for="(item,index) in cardList" :key="index">
          <div :class="['card', `${items.color}`]" v-for="(items,indexs) in item.children" :key="indexs">
           <div class="images">
              <div class="i_title">{{ items.title }}</div>
              <div class="num">{{ items.num }}</div>
           </div>
           <div class="no">{{ items.no }}</div>
          </div>
        </div>
      </div>
      <div class="swich">
        <div class="switch">
          <a-switch v-model:checked="switched" checked-children="一键关断" un-checked-children="一键开启"/>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount,onMounted, computed } from "vue";
import { message, Modal } from "ant-design-vue";
import { useStore } from 'vuex';
import moment from 'moment';
import 'moment/dist/locale/zh-cn';
import { 
  queryComponentListTo
} from "@/api/list.js";

const props = defineProps({
  viewPowerstationValue: {
    type: Object,
    default: () => {}
  }
});

const allData = ref(0)
const normalData = ref(0)
const abnormalData = ref(0)
const offLineData = ref(0)


const selectKwhValue = ref("0")
const selectKwh = ref([
  {
    label:"组件电压",
    value:"0"
  }
]) 
const cardList = ref(
  // [

  //   {
  //     label:"",
  //     children:[
  //       {
  //         title:"650W",
  //         num:"30.5V",
  //         no:"W1-1-1",
  //         color:"blue"
  //       },
  //       {
  //         title:"520W",
  //         num:"",
  //         no:"W1-1-1",
  //         color:"yellow"
  //       },
  //       {
  //         title:"650W",
  //         num:"",
  //         no:"W1-1-1",
  //         color:"yellow"
  //       },
  //       {
  //         title:"650W",
  //         num:"",
  //         no:"W1-1-1",
  //         color:"yellow"
  //       },
  //       {
  //         title:"650W",
  //         num:"",
  //         no:"W1-1-1",
  //         color:"red"
  //       },
  //       {
  //         title:"650W",
  //         num:"25.5V",
  //         no:"W1-1-1",
  //         color:"blue"
  //       },
  //        {
  //         title:"650W",
  //         num:"",
  //         no:"W1-1-1",
  //         color:"gray"
  //       },
  //     ]
  //   }
  // ]
)
const switched = ref(false)
// 测试接口
async function getQueryComponentListTo() {
  const today = new Date();
  const day = today.getFullYear() + '-' + 
              String(today.getMonth() + 1).padStart(2, '0') + '-' + 
              String(today.getDate()).padStart(2, '0');

  try {
    // data方式传参
    const res = await queryComponentListTo(new URLSearchParams({
      powerStationId: props.viewPowerstationValue.id,
      date: day
    }));
    if (res?.data?.code === 0) {
     
      const list = res.data.reModel
      allData.value = list.data.length
      normalData.value = list.data.filter((item) => item.status == 1).length
      abnormalData.value = list.data.filter((item) => item.status == 3).length
      offLineData.value = list.data.filter((item) => item.status == 2).length
      let cardData = [
        {
           label:"",
            children:[]
        }
      ]
      list.data.forEach((item) => {
        // outputCurrent 电流
        // outputVoltage 电压
        // 功率等于电压*电流/1000000
        // 电压=电压/1000
        let obj = {
          title:!item.outputCurrent || !item.outputVoltage? "0W":  (Number(item.outputVoltage * item.outputCurrent / 1000000).toFixed(1)) + "W",
          num:!item.outputCurrent || !item.outputVoltage? "0V": (Number(item.outputVoltage / 1000).toFixed(1)) + "V",
          no:item.chipId,
          color: item.status == 1?"blue": item.status == 2? 'gray': 'red'
        }
        cardData[0].children.push(obj)
      })
cardList.value = cardData
 console.log(list,"queryComponentListTo")
      // message.success('接口调用成功');
    } else {
      // message.warning('接口调用失败');
    }
  } catch (error) {
    // message.error('接口调用出错');
  }
}
onMounted(async () => {
    console.log(props.viewPowerstationValue,"props222")
    getQueryComponentListTo()
});
</script>

<style lang="less" scoped>
.overview {
  background: #fff;
  height: 730px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20px;
  .overview_item{
    height: 100%;
    padding: 43px 0px;
     .title{
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        .line{
            display: block;
            width:16px;
            height: 0px;
            transform: rotate(90deg);
            border: 2px solid rgba(230, 0, 18, 1);
            border-radius: 5px;
        }
        .text{
            font-size: 18px;
            font-weight: 400;
            color: rgba(56, 56, 56, 1);
        }
    }
  }
  .left{
        width: 45%;
        position:relative;
    .card{
      width: 100%;
      height: 100px;
      border-radius: 20px;
      background: rgba(247, 247, 247, 1);
      margin-top: 20px;
      padding: 20px 0;
      display: flex;
      align-items: center;
      .card_item{
        width: 20%;
        height: 100%;
        margin-left: 40px;
        img{
          width: 28px;
          height: 28px;
        }
        .text{
          margin-top: 0px;
          .label{
            font-size: 16px;
            color: rgba(102, 102, 102, 1);
          }
          .value{
            font-size: 26px;
            font-weight: 700;
            color: rgba(61, 61, 61, 1);
            margin-left: 5px;
          }
        }
      }
      .border_r{
        border-right: 1px solid rgba(224, 224, 224, 1);
      }
    }
    .select_content{
      width: 100%;
      height: 40px;
      margin-top: 10px;
        .select{
          width: 150px;
          height: 30px;
          .ant-select{
            width: 100%;
          }
        }
    }
    .wl_icon{
      width: 338px;
      height: 118px;
      background: url(../../../assets/images/index/wl_icon.png) no-repeat 0 0;
      background-size: 100% 100%;
      position:absolute;
      left: 20px;
      bottom: 20px;
    }
  }
  .right{
    width: 55%;
    display: flex;
    .line{
      width: 104px;
      height: 100%;
      position: relative;
      margin-left: 56px;
      .line_item{
        width: 100%;
        height: 94px;
        position: absolute;
        border-radius: 10px;
        background: #000;
        .query{
          width: 100%;
          height: 72px;
          border-radius: 10px;
          background: rgba(255, 255, 255, 1);
          border: 1.3px solid rgba(214, 214, 214, 1);
          padding: 8px;
          position: relative;
          .text{
            width: 50%;
            display: flex;
            align-items: center;
            font-weight: 700;
            position: absolute;
            margin: auto;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
          }
          .line_1{
            font-size: 1em;
          }
          .line_2{
            font-size: 2em;
          }
          .line_3{
            font-size: 2em;
          }
          .dot{
            width: 13px;
            height: 13px;
            background: rgba(115, 199, 100, 1);
            border-radius: 50%;
          }
        }
        .num{
          font-size: 12px;
          color: rgba(255, 255, 255, 1);
          text-align: center;
          height: 20px;
          line-height: 20px;
        }
      }
      .line_top{
        position: absolute;
        top: 30%;
      }
      .line_bottom{
        position: absolute;
        bottom: 20%;
      }
    }
    .right_card{
      width: 70%;
      height: 100%;
      margin-left: 53px;
      .card_item{
        width: 100%;
        height: 100px;
        margin-bottom: 18px;
        display: flex;
        flex-wrap: wrap;
        .card{
          width: 65px;
          height: 100%;
          margin-right: 15px;
          .images{
            overflow: hidden;
            width: 100%;
            height: 85px;
            .i_title{
              width: 100%;
              color: rgba(255, 255, 255, 1);
              font-size: 14px;
              font-weight: 700;
              text-align: center;
              margin-top: 18px;
            }
            .num{
               width: 100%;
              text-align: center;
              font-size: 12px;
               color: rgba(255, 255, 255, 1);
               margin-top: 0px;
            }
          }
          .no{
            width: 100%;
            height: 15px;
            text-align: center;
            line-height: 15px;
            font-size:8px;
            color: #fff;
          }
        }
        .blue{
          background: url(../../../assets/images/index/station_view_normal_100.webp) no-repeat 0 0;
          background-size: 100% 100%;
        }
        .red{
          background: url(../../../assets/images/index/station_view_normal_100.webp) no-repeat 0 0;
          background-size: 100% 100%;
        }
        .yellow{
          background: url(../../../assets/images/index/station_view_abnormal.webp) no-repeat 0 0;
          background-size: 100% 100%;
        }
        .gray{
          background: url(../../../assets/images/index/station_view_offline.webp) no-repeat 0 0;
          background-size: 100% 100%;
        }
      }
    }
    .swich{
      width: 30%;
      height: 100%;
      margin-left: 20px;
      .switch{
        width: 100%;
        height: auto;
      }
      
    }
  }
}
</style>