<template>
  <div class="collector wh100 plr16">

    <div class="table-part w100" v-if="!flag.value">
      <Search :searchData="searchData" @handleEvent="handleTableFilter" />
      <div class="btns flex-row-center-between">
        <div class="btn-left flex-row-center-start nowrap">
          <div class="btn" @click="() => handleEvent('select-group')">
            <span>{{ locales.xuanzezuchuan }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('view-group')"
          >
            <span>{{ locales.chakanzuchuan }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('select-relay')"
          >
            <span>{{ locales.xuanzezhongjiqi }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('view-relay')"
          >
            <span>{{ locales.chakanzhongjiqi }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('view-version')"
          >
            <span>{{ locales.chaxunbanben }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('import-file')" style="display: flex;justify-content: center;align-items: center;"
          >
            <span>{{ locales.daoru }}</span>
          </div>
        </div>
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('add')">
            {{ locales.xinzengshebei }}
          </button>
          <button
            class="btn btn-delete ml16"
            @click="() => handleEvent('delete')"
          >
            {{ locales.shanchu }}
          </button>
        </div>
      </div>
      <Table
        :refreshTableData="() => handleQuery(queryData)"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      >
        <template #action="{ record }">
          <div class="action-buttons">
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('view', record)">
              {{ locales.chakan }}
            </span>
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('update', record)">
              {{ locales.xiugai }}
            </span>
            <span class="action-btn action-btn-danger" @click="() => handleRowEvent('delete', record)">
              {{ locales.shanchu }}
            </span>
          </div>
        </template>
      </Table>
    </div>
    <DeepTable
      v-else-if="flag.value == 'deep'"
      :btns="btns"
      :api="curApi"
      :params="params"
      :refresh="refresh"
      :columns="deepColumn"
      @handleBtnClick="handleBtnClick"
    />
    <Form
      v-else
      :isEdit="flag.value == 'add' || flag.value == 'update'"
      :formData="flag.value == 'view' ? viewData : formData"
      @handleEvent="handleFormEvent"
    ></Form>
    <a-modal
      v-model:open="openVersion"
      :title="locales.chaxunbanben"
      class="collector-modal version"
    >
      <div class="modal-box flex-row-center-center nowrap">
        <button
          class="btn flex-row-center-center"
          @click="() => handleVersionQuery(1)"
        >
          查询自身版本
        </button>
        <button
          class="btn ml16 flex-row-center-center"
          @click="() => handleVersionQuery(2)"
        >
          查询所属组件版本
        </button>
      </div>
    </a-modal>
    <a-modal
      v-model:open="openImport"
      :title="locales.yunzhongduan"
      :okText="locales.tijiao"
      class="collector-modal import"
      @ok="uploadCloud"
    >
      <div class="modal-box flex-row-center-start nowrap">
        <span>{{ locales.shangchuanwenjian }}：</span>
        <input type="file" @change="handleFileUpload" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const {
  collectorColumn,
  selectGroupColumn,
  selectRelayColumn,
} = require("@/db.js");
import {
  getDataList,
  saveOrUpdate,
  deleteCloud,
  queryComponentGroupList,
  queryRelayListForCloud,
  versionQueryTask,
  cloudUpload,
  selectComponentGroup,
  selectRelay,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";
const Search = defineAsyncComponent(() =>
  import("../components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);
const DeepTable = defineAsyncComponent(() =>
  import("@/views/components/DeepTable/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();

let flag = ref({});
const userName = sessionStorage.getItem("userName") || "";
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

onBeforeMount(() => {
  tableData.columns = collectorColumn;
  handleQuery();
});

const searchData = ref([
  {
    label: locales.value.name,
    key: "cloudName",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.lurushijan,
    key: "createTime",
    value: undefined,
    type: "rangeTime",
  },
  {
    label: locales.value.xuliehao,
    key: "serialNo",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.xinghao,
    key: "model",
    value: undefined,
    type: "input",
    width: 30,
  },
  {
    label: locales.value.dianzhanmingcheng,
    key: "powerStationName",
    value: undefined,
    type: "input",
  },
  // {
  //   label: locales.value.shengchanshang,
  //   key: "producers",
  //   value: undefined,
  //   type: "input",
  // },
]);
let formData = ref([
  {
    label: locales.value.name,
    key: "cloudName",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.xuliehao,
    key: "serialNo",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.xinghao,
    key: "model",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.shengchanshang,
    key: "producers",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.caijiqibiaoshi,
    key: "imei",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.chuangjianren,
    key: "createUserName",
    value: userName,
    type: "input",
    disabled: true,
  },
  {
    label: locales.value.miaoshu,
    key: "describetion",
    value: undefined,
    type: "textarea",
  },
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}
async function handleQuery(data) {
  let res = await getDataList("cloudter/queryCloudTerminalList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    cloudName: data?.cloudName || "",
    createTimeStart: data?.createTime?.[0] || "",
    createTimeEnd: data?.createTime?.[1] || "",
    serialNo: data?.serialNo || "",
    model: data?.model || "",
    powerStationName: data?.powerStationName || "",
    producers: data?.producers || "",
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource = reModel.data;
    }
  }
}

let viewData = ref([]);
async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增or修改
    data.createUserName = undefined;
    if (flag.value.value == "update") data.id = tableData.selectedRowKeys[0];

    let res = await saveOrUpdate("cloudter", data);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

// 处理行操作事件
async function handleRowEvent(type, record) {
  // 设置选中的行
  tableData.selectedRowKeys = [record.id];
  // 调用原有的事件处理函数
  await handleEvent(type);
}

let openVersion = ref(false);
let openImport = ref(false);
async function handleEvent(type) {
  if (type == "delete") {
    if (!tableData.selectedRowKeys?.length)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  } else if (type != "add" && type != "import-file") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }

  if (type == "add") {
    // 新增
    flag.value = { label: locales.value.xinzeng, value: "add" };
    formData.value.forEach((v) => {
      if (!v.disabled) v.value = undefined;
    });
  } else if (type == "update") {
    // 修改
    flag.value = { label: locales.value.xiugai, value: "update" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    formData.value.forEach((v) => {
      v.value = curData[v.key];
    });
  } else if (type == "view") {
    // 查看
    flag.value = { label: locales.value.chakan, value: "view" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );

    viewData.value =
      formData.value
        .filter((e) => e.key != "describetion")
        .map((v) => {
          v.value = curData[v.key];
          let { required, ...others } = v;
          return {
            ...others,
            disabled: true,
          };
        }) || [];

    let {
      softVersion,
      hardVersion,
      bootPartition,
      bomId,
      mcu,
      updateTime,
      createTimeCh,
      describetion,
      powerStationName,
    } = curData;
    let arr = [
      {
        label: locales.value.ruanjianbanbenhao,
        key: "softVersion",
        value: softVersion,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.fenqu,
        key: "bootPartition",
        value: bootPartition,
        type: "input",
        disabled: true,
      },
      {
        label: "mcu",
        key: "mcu",
        value: mcu,
        type: "input",
        disabled: true,
      },
      {
        label: "bomId",
        key: "bomId",
        value: bomId,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.yingjianbanbenhao,
        key: "hardVersion",
        value: hardVersion,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.gengxinshijian,
        key: "updateTime",
        value: updateTime,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.chaungjianshijian,
        key: "createTimeCh",
        value: createTimeCh,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.dianzhanmingcheng,
        key: "powerStationName",
        value: powerStationName,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.miaoshu,
        key: "describetion",
        value: describetion,
        type: "textarea",
        disabled: true,
      },
    ];
    viewData.value = [...viewData.value, ...arr];
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deleteCloud({
          id: tableData.selectedRowKeys.join(","),
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "select-group") {
    // 选择组串
    flag.value = { label: locales.value.xuanzezuchuan, value: "deep" };
    curApi.value = queryComponentGroupList;
    params.value = {
      cloudIdFlag: "addComponent",
      cloudId: tableData.selectedRowKeys[0],
    };
    deepColumn.value = selectGroupColumn;
    btns.value = [
      { label: locales.value.queren, value: "group-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "view-group") {
    // 查看组串
    flag.value = { label: locales.value.chakanzuchuan, value: "deep" };
    curApi.value = queryComponentGroupList;
    params.value = {
      cloudIdFlag: "selectComponent",
      cloudId: tableData.selectedRowKeys[0],
    };
    deepColumn.value = selectGroupColumn;
    btns.value = [
      { label: locales.value.yichu, value: "group-remove" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "select-relay") {
    // 选择中继器
    flag.value = { label: locales.value.xuanzezhongjiqi, value: "deep" };
    // let curData = tableData.dataSource.find(
    //   (e) => e.id == tableData.selectedRowKeys[0]
    // );
    curApi.value = queryRelayListForCloud;
    params.value = {
      // cloudId: curData?.imei || "",
      cloudId: "",
      operationFlag: "selectRelay",
    };
    deepColumn.value = selectRelayColumn;
    btns.value = [
      { label: locales.value.queren, value: "relay-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "view-relay") {
    // 查看中继器
    flag.value = { label: locales.value.chakanzhongjiqi, value: "deep" };
    curApi.value = queryRelayListForCloud;
    params.value = {
      cloudId: tableData.selectedRowKeys[0],
      operationFlag: "viewRelay",
    };
    deepColumn.value = selectRelayColumn;
    btns.value = [
      { label: locales.value.yichu, value: "relay-remove" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "view-version") {
    // 查询版本
    openVersion.value = true;
    flag.value = {};
  } else if (type == "import-file") {
    // 导入
    openImport.value = true;
    flag.value = {};
  }
}

async function handleVersionQuery(queryType) {
  let res = await versionQueryTask({
    queryType,
    id: tableData.selectedRowKeys[0],
  });
  if (res?.data?.code === 0) {
    message.success(locales.value.caozuochenggong);
    handleQuery();
  } else message.warning(locales.value.caozuoshibai);
  openVersion.value = false;
}
let selectedFile = ref(null);
function handleFileUpload(event) {
  selectedFile.value = event.target.files[0]; // 获取第一个文件
  if (selectedFile.value) {
    let nameArr = selectedFile.value.name.split(".") || [];
    let type = nameArr[nameArr.length - 1];
    const typeList = ["xls", "xlsx", "csv"];
    if (typeList.findIndex((e) => e == type) < 0) {
      return message.warning(locales.value.excelgeshi);
    }
  }
}

async function uploadCloud() {
  // 提交
  try {
    const formData = new FormData();
    formData.append("upfile", selectedFile.value);
    await cloudUpload(formData);
    message.success(locales.value.caozuochenggong);

    openImport.value = false;
  } catch (error) {
    message.warning(locales.value.caozuoshibai);
    openImport.value = false;
  }
}

let btns = ref([]);
let curApi = ref(null);
let params = ref({});
let deepColumn = ref([]);
let refresh = ref(false);
function handleBtnClick({ type, value }) {
  if (refresh.value) refresh.value = false;
  if (type == "back") flag.value = {};
  else {
    if (value?.length) {
      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = null;
          if (type == "group-confirm") {
            res = await selectComponentGroup({
              cloudId: tableData.selectedRowKeys[0],
              id: value.join(","),
              cloudIdFlag: "addComponent",
            });
          } else if (type == "relay-confirm") {
            res = await selectRelay({
              cloudId: tableData.selectedRowKeys[0],
              // cloudId: "",
              powerStationId: "",
              id: value.join(","),
              operationFlag: "selectRelay",
            });
          } else if (type == "group-remove") {
            res = await selectComponentGroup({
              id: value.join(","),
              cloudIdFlag: "selectComponent",
            });
          } else if (type == "relay-remove") {
            res = await selectRelay({
              id: value.join(","),
              operationFlag: "viewRelay",
            });
          }
          if (res.data.code === 0) {
            message.success(locales.value.caozuochenggong);
            refresh.value = true;
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    } else message.info(locales.value.zhengquexuanzecaozuoxiang);
  }
}
</script>

<style lang="less" scoped>
.collector {
  .btns {
    margin: 20px 0 12px;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;

    .action-btn {
      cursor: pointer;
      font-size: 14px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &.action-btn-primary {
        color: #1890ff;

        &:hover {
          color: #40a9ff;
          background-color: #e6f7ff;
        }
      }

      &.action-btn-danger {
        color: #ff4d4f;

        &:hover {
          color: #ff7875;
          background-color: #fff2f0;
        }
      }
    }
  }
}
</style>

<style lang="less">
.collector-modal {
  width: 450px !important;
  .modal-box {
    padding: 20px;
    .btn {
      width: 140px !important;
      border-radius: 2px !important;
    }
  }
  &.version {
    .ant-modal-footer {
      .ant-btn-primary {
        display: none !important;
      }
    }
  }
}
</style>