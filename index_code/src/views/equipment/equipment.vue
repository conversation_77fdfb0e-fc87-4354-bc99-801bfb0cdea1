<template>
  <div class="wh100 plr16">
    <a-breadcrumb class="p16">
      <img
          src="@/assets/images/red_line.png"
          alt=""
          style="width: 5px; height: 18px; margin-right: 8px"
        />
      <a-breadcrumb-item class="cursor" @click="() => (flag = {})">{{
        locales.shebeiguanli
      }}</a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" v-if="flag.value">{{
        flag.label
      }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="table-part w100" v-if="!flag.value">

      <!-- 搜索条件区域 -->
      <Search :searchData="searchData" @handleEvent="handleTableFilter" />
       <!-- 操作按钮区域 -->
      <div class="btns flex-row-center-between">
        <div class="btn-left flex-row-center-start nowrap">
          <button class="btn add-device-btn" @click="() => handleEvent('add')">
            <img src="@/assets/images/plus.png" alt="新增" class="btn-icon" />
            {{ locales.xinzengshebei }}
          </button>
        </div>
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('view-smart-optimizer')">
            {{ locales.chakanzhinengyouhuaqi }}
          </button>
          <button class="btn  ml16" @click="() => handleEvent('view-smart-gateway')">
            {{ locales.chakanzhinnengwangguan }}
          </button>
          <button class="btn  ml16" @click="() => handleEvent('view-relay')">
            {{ locales.chakanzhongjiqi }}
          </button>
          <button class="btn  ml16" @click="() => handleEvent('view-station-string')">
            {{ locales.chakandianzhanzhuchuan }}
          </button>
          <button class="btn  ml16" @click="() => handleEvent('view-inverter')">
            {{ locales.chakannibianqi }}
          </button>
        </div>
      </div>
       <!-- 表格区域 -->
      <Table
        :refreshTableData="() => handleQuery(queryData)"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      >
        <template #status="{ text }">
          <span :class="`status-btn-${text} flex-row-center-center`">
            {{ statusOpt.find((e) => e.value == text)?.label || "" }}
          </span>
        </template>
        <template #action="{ record }">
          <div class="action-buttons">
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('view', record)">
              {{ locales.chakan }}
            </span>
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('update', record)">
              {{ locales.xiugai }}
            </span>
            <span class="action-btn action-btn-danger" @click="() => handleRowEvent('delete', record)">
              {{ locales.shanchu }}
            </span>
          </div>
        </template>
      </Table>
    </div>
    <DeepTable
      v-else-if="flag.value == 'deep'"
      :btns="btns"
      :tabs="tabs"
      :api="curApi"
      :params="params"
      :refresh="refresh"
      :columns="deepColumn"
      :searchData="searchDeepData"
      @handleTabChange="handleTabChange"
      @handleBtnClick="handleBtnClick"
    />
    <ComponentList
      v-else-if="flag.value == 'component-list'"
    />
    <Collector
      v-else-if="flag.value == 'collector'"
    />
    <Repeater
      v-else-if="flag.value == 'repeater'"
    />
    <GroupList
      v-else-if="flag.value == 'group-list'"
    />
      <Inverter
      v-else-if="flag.value == 'inverter'"
    />
    <EquipmentManager
      v-else-if="flag.value == 'equipment-manager'"
      :viewData="viewData"
      @handleEvent="handleFormEvent"
    />
    <Form
      v-else
      :isEdit="flag.value == 'add' || flag.value == 'update'"
      :formData="flag.value == 'view' ? viewData : formData"
      @handleEvent="handleFormEvent"
    ></Form>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import { useStore } from 'vuex';
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const {
  stationColumn3,
  selectCloudColumn,
  selectInverterColumn,
  selectComponentColumn,
} = require("@/db.js");
import {
  getDataList,
  saveOrUpdate,
  deletePowerStation,
  selectCloudTerminal,
  selectInverter,
  selectComponent,
  selectEquipment,
  stationDeleteEquipment,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";
const Search = defineAsyncComponent(() =>
  import("../components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);
const DeepTable = defineAsyncComponent(() =>
  import("@/views/components/DeepTable/index.vue")
);
const ComponentList = defineAsyncComponent(() =>
  import("@/views/componentList/componentList.vue")
);
const Collector = defineAsyncComponent(() =>
  import("@/views/collector/collector.vue")
);
const Repeater = defineAsyncComponent(() =>
  import("@/views/repeater/repeater.vue")
);
const GroupList = defineAsyncComponent(() =>
  import("@/views/groupList/groupList.vue")
);
const Inverter = defineAsyncComponent(() =>
  import("@/views/inverter/inverter.vue")
);
const EquipmentManager = defineAsyncComponent(() =>
  import("@/views/equipment/equipment_manager.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
const store = useStore();
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

// 定义 emit 用于向父组件发送事件
const emit = defineEmits(['navigate-to-page']);

let flag = ref({});

onBeforeMount(() => {
  tableData.columns = stationColumn3;
  handleQuery();
});

const typeOpt = [
  { label: "V08", value: 1 },
  { label: "PLC", value: 2 },
];
const layoutTypeOpt = [
  { label: "组件模式", value: 1 },
  { label: "组串模式", value: 2 },
];
const statusOpt = [
  { label: "待调试", value: 1 },
  { label: "已调试", value: 2 },
];
const gradeOpt = [
  { label: "微型", value: 1 },
  { label: "小型", value: 2 },
  { label: "中型", value: 3 },
  { label: "大型", value: 4 },
  { label: "特大型", value: 5 },
];
const searchData = ref([
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.chaungjianshijian,
    key: "createTime",
    value: undefined,
    type: "rangeTime",
  },
  {
    label: "编号",
    key: "systemNo",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.status,
    key: "status",
    value: undefined,
    type: "select",
    options: statusOpt,
    width: 30,
  },
  {
    label: "电站位置",
    key: "area",
    value: [],
    type: "cascader",
  },
  {
    label: locales.value.dianzhanjibie,
    key: "grade",
    value: undefined,
    type: "select",
    options: gradeOpt,
  },
  {
    label: "详细地址",
    key: "streetName",
    value: undefined,
    type: "input",
    width: 30,
  },
]);
let formData = ref([
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.dianzhangonglv,
    key: "power",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.dianzhanleixing,
    key: "type",
    value: undefined,
    type: "select",
    options: typeOpt,
    required: true,
  },
  {
    label: locales.value.bujumoshi,
    key: "layoutType",
    value: undefined,
    type: "select",
    options: layoutTypeOpt,
    required: true,
  },
  {
    label: locales.value.guojia,
    key: "countriesId",
    value: undefined,
    type: "cascader",
    level: 1,
  },
  {
    label: locales.value.shengdiqu,
    key: "province",
    value: undefined,
    type: "cascader",
    level: 2,
  },
  {
    label: locales.value.chengshi,
    key: "cityId",
    value: undefined,
    type: "cascader",
    level: 3,
  },
  {
    label: "详细地址",
    key: "streetName",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.xuanzejingweidu,
    key: "coordinate",
    value: undefined,
    type: "input-search",
  },
  {
    label: locales.value.jingdu,
    key: "longitude",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.weidu,
    key: "latitude",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.richushijian,
    key: "sunuptime",
    value: undefined,
    type: "time",
    placeholder: locales.value.richushijiangeshi,
    required: true,
  },
  {
    label: locales.value.riluoshijian,
    key: "sundowntime",
    value: undefined,
    type: "time",
    placeholder: locales.value.riluoshijiangeshi,
    required: true,
  },
  {
    label: locales.value.caijishijianjiange,
    key: "collectGap",
    value: undefined,
    type: "input",
    required: true,
  },
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}
async function handleQuery(data) {
  let res = await getDataList("powerstation/queryPowerStationList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    systemName: data?.systemName || "",
    createTimeStart: data?.createTime?.[0] || "",
    createTimeEnd: data?.createTime?.[1] || "",
    systemNo: data?.systemNo || "",
    createUserName: data?.createUserName || "",
    countriesId: data?.area[0] || "",
    province: data?.area[1] || "",
    cityId: data?.area[2] || "",
    streetName: data?.streetName || "",
    status: data?.status || "",
    grade: data?.grade || "",
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource = reModel.data.map((e) => {
        return {
          ...e,
          grade: gradeOpt.find((v) => v.value == e.grade)?.label || "",
          createTimeCh: e.createTimeCh ? new Date(e.createTimeCh).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          }).replace(/\//g, '/') : "",
          location: [e.countries, e.provinceName, e.cityName].filter(Boolean).join('-') || "",
        };
      });
    }
  }
}
async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增or修改
    let { createUserName, coordinate, sunuptime, sundowntime, ...others } =
      data;
    others.sunuptimech = sunuptime;
    others.sundowntimech = sundowntime;
    if (flag.value.value == "update") others.id = tableData.selectedRowKeys[0];

    let res = await saveOrUpdate("powerstation", others);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

let viewData = ref([]);
let tabs = ref([]);
let curApi = ref(null);
let params = ref({});
let deepColumn = ref([]);
let searchDeepData = ref([]);
let btns = ref([]);

// 处理行操作事件
async function handleRowEvent(type, record) {
  // 设置选中的行
  tableData.selectedRowKeys = [record.id];
  // 调用原有的事件处理函数
  await handleEvent(type);
}

async function handleEvent(type) {
  if (type == "delete") {
    if (!tableData.selectedRowKeys?.length)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  } else if (type != "add" &&
             type != "view-smart-optimizer" &&
             type != "view-smart-gateway" &&
             type != "view-relay" &&
             type != "view-station-string" &&
             type != "view-inverter") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }

  if (type != "view-device") tabs.value = [];
  if (type != "select-owners") searchDeepData.value = [];

  if (type == "add") {
    // 新增
    flag.value = { label: locales.value.xinzeng, value: "add" };
    formData.value.forEach((v) => {
      if (!v.disabled) v.value = undefined;
    });
  } else if (type == "update") {
    // 修改
    flag.value = { label: locales.value.xiugai, value: "update" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    formData.value.forEach((v) => {
      if (v.key != "createUserName" && v.key != "coordinate") {
        if (["countriesId", "province", "cityId"].indexOf(v.key) > -1) {
          v.value = Number(curData[v.key]) || undefined;
        } else v.value = curData[v.key];
      }
    });
  } else if (type == "view") {
    // 查看 - 跳转到设备管理器页面
    flag.value = { label: locales.value.chakan, value: "equipment-manager" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );

    // 准备传递给设备管理器的数据
    viewData.value = {
      // 基本信息
      stationInfo: {
        id: curData.id,
        systemName: curData.systemName,
        power: curData.power,
        type: typeOpt.find(t => t.value === curData.type)?.label || curData.type,
        layoutType: layoutTypeOpt.find(t => t.value === curData.layoutType)?.label || curData.layoutType,
        location: curData.location,
        streetName: curData.streetName,
        longitude: curData.longitude,
        latitude: curData.latitude,
        sunuptime: curData.sunuptime,
        sundowntime: curData.sundowntime,
        collectGap: curData.collectGap,
        createTimeCh: curData.createTimeCh,
        grade: curData.grade
      },
      // 设备统计信息
      equipmentStats: {
        cloudTerminalNum: curData.cloudTerminalNum || 0,
        componentNum: curData.componentNum || 0,
        inverterNum: curData.inverterNum || 0,
        relayNum: curData.relayNum || 0,
        stringNum: curData.stringNum || 0
      },
      // 原始数据
      rawData: curData
    };

    // 存储到 store 中
    store.commit('setEquipmentViewData', viewData.value);
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deletePowerStation({
          id: tableData.selectedRowKeys.join(","),
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "view-smart-optimizer") {
    // 查看智能优化器 - 显示组件列表页面作为子页面
    flag.value = { label: "智能优化器", value: "component-list" };
  } else if (type == "view-smart-gateway") {
    // 查看智能网关 - 跳转到采集器页面
    flag.value = { label: "智能网关", value: "collector" };
  } else if (type == "view-relay") {
    // 查看中继器 - 跳转到中继器页面
    flag.value = { label: "中继器", value: "repeater" };
  } else if (type == "view-station-string") {
    // 查看电站组串 - 跳转到组串列表页面
    flag.value = { label: "电站组串", value: "group-list" };
  } else if (type == "view-inverter") {
    // 查看逆变器 - 跳转到中继器页面（根据您的要求）
    flag.value = { label: "逆变器", value: "inverter" };
  }

  if (flag.value.value != "deep") btns.value = [];
}

let equipmentType = ref(1);
function handleTabChange(tab) {
  equipmentType.value = tab;
  deepColumn.value =
    tab == 1
      ? selectCloudColumn
      : tab == 2
      ? selectInverterColumn
      : selectComponentColumn;
  params.value = {
    powerStationId: tableData.selectedRowKeys[0],
  };
  curApi.value =
    tab == 1
      ? selectCloudTerminal
      : tab == 2
      ? selectInverter
      : selectComponent;
}

let refresh = ref(false);
function handleBtnClick({ type, value }) {
  if (refresh.value) refresh.value = false;
  if (type == "back") flag.value = {};
  else {
    if (value?.length) {
      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = null;
          if (type == "cloud-confirm" || type == "inverter-confirm") {
            if (value?.length != 1)
              return message.info(locales.value.zhengquexuanzecaozuoxiang);
            res = await selectEquipment({
              id: tableData.selectedRowKeys[0],
              equipmentType: type == "inverter-confirm" ? 2 : 1, // 设备类型 1云终端 2逆变器 3组件
              equipmentId: value[0],
            });
          } else if (type == "remove") {
            res = await stationDeleteEquipment({
              id: tableData.selectedRowKeys[0],
              equipmentType: equipmentType.value, // 设备类型 1云终端 2逆变器 3组件
              equipmentId: value.join(","),
            });
          } else if (type == "owner-confirm") {
            if (value?.length != 1)
              return message.info(locales.value.zhengquexuanzecaozuoxiang);
            res = await selectOwner({
              memberId: value[0],
              powerStationId: tableData.selectedRowKeys[0],
            });
            message.info(res?.data?.message || "");
            return;
          }
          if (res.data.code === 0) {
            message.success(locales.value.caozuochenggong);
            refresh.value = true;
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    } else message.info(locales.value.zhengquexuanzecaozuoxiang);
  }
}


</script>


<style lang="less">
.ant-modal-confirm-content {
  font-size: 16px !important;
  color: #0d0c12 !important;
}
</style>
