<template>
  <div class="equipment-manager wh100" style="background-color: white;">
    <!-- 主要内容区域 - 左右布局 -->
    <div class="main-content flex-row-center-start wh100">
      <!-- 左侧区域 -->
      <div class="left-section">

         <a-breadcrumb class="p16">
      <img
          src="@/assets/images/red_line.png"
          alt=""
          style="width: 5px; height: 18px; margin-right: 8px"
        />
      <a-breadcrumb-item class="cursor" @click="() => (flag = {})">{{
        locales.dianzhangshebei
      }}</a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" v-if="flag.value">{{
        flag.label
      }}</a-breadcrumb-item>
    </a-breadcrumb>

        <!-- 第二行：左侧图片 + 右侧网格 -->
         <div style="background-color: rgba(245, 245, 245, 1); padding: 30px 25px;
         border-radius: 12px;">
        <div class="second-row flex-row-center-start" style="align-items: stretch;">
          <!-- 左侧图片 -->
          <div class="station-image">
            <img src="@/assets/images/station1.webp" alt="电站图片1" />
          </div>

          <div class="weather-info-column" style="display: flex; flex-direction: column; justify-content: space-between; margin-left: 5px;">
            <div class="weather-row"  style="margin-bottom: 12px;">
              <span>{{ stationViewData.stationInfo?.systemName || '测试电站' }}</span>
            </div>
            <div class="weather-row"  style="margin-bottom: 12px;">
              <span>{{ stationViewData.stationInfo?.location || '中国' }}</span>
            </div>
             <div class="weather-row"  style="margin-bottom: 12px;">
              <span>{{ stationViewData.stationInfo?.createTimeCh || '2010-10-01' }}</span>
            </div>
          </div>
          

          <!-- 第二列：天气信息 -->
          <div class="weather-info-column" style="display: flex; flex-direction: column; justify-content: space-between; margin-left: 5px;">
            <div class="weather-row" style="margin-bottom: 12px;">
              <div class="weather-icon" style="display: flex; align-items: center;">
                <img src="@/assets/images/location.png" alt="地理位置" style="width: 15px; height: 15px; margin-right: 8px; object-fit: contain; display: block;" />
                <span>嘉兴·阴天 10°C-20°C</span>
              </div>
            </div>
            <div class="weather-row" style="margin-bottom: 12px;">
              <div class="weather-icon" style="display: flex; align-items: center;">
                <img src="@/assets/images/sun.png" alt="阳光强度" style="width: 15px; height: 15px; margin-right: 8px; object-fit: contain; display: block;" />
                <!-- <span>日照时间 {{ stationViewData.stationInfo?.sunuptime}} - {{ stationViewData.stationInfo?.sundowntime}}</span> -->
                <span>日照时间 06:00-18:01</span>

              </div>
            </div>
            <div class="weather-row" style="margin-bottom: 12px;">
              <div class="weather-icon" style="display: flex; align-items: center;">
                <img src="@/assets/images/orientation.png" alt="朝向角度" style="width: 15px; height: 15px; margin-right: 8px; object-fit: contain; display: block;" />
                <span>经度 {{ stationViewData.stationInfo?.longitude || 31.2}} 纬度 {{ stationViewData.stationInfo?.latitude || 121.5}}</span>
              </div>
            </div>
          </div>

    
        </div>

        <!-- 5个卡片组件 - 三行布局 -->
        <div class="cards-section">
          <!-- 第一行 - 两个卡片 -->
          <div class="cards-row">
            <div class="equipment-card" v-for="(card, index) in equipmentCards.slice(0, 2)" :key="index">
              <div class="card-image">
                <img :src="card.image" :alt="card.name" />
              </div>
              <div class="card-content">
                <div class="content-row">{{ card.name }}

                <span class="config-badge">
                  设备配置
                </span>

                </div>
                <div class="content-row">{{ card.type }}</div>
                <div class="content-row">
                  {{ card.status }}
                
                <span>
                数量{{ card.count }}个
                </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二行 - 两个卡片 -->
          <div class="cards-row">
            <div class="equipment-card" v-for="(card, index) in equipmentCards.slice(2, 4)" :key="index + 2">
              <div class="card-image">
                <img :src="card.image" :alt="card.name" />
              </div>
              <div class="card-content">
                <div class="content-row">{{ card.name }}

       <span class="config-badge">
                  设备配置
                </span>

                </div>
                <div class="content-row">{{ card.type }}</div>
                <div class="content-row">{{ card.status }}
            <span>
                数量{{ card.count }}个
                </span>


                </div>
              </div>
            </div>
          </div>

          <!-- 第三行 - 一个卡片 -->
          <div class="cards-row">
            <div class="equipment-card" v-for="(card, index) in equipmentCards.slice(4, 5)" :key="index + 4">
              <div class="card-image">
                <img :src="card.image" :alt="card.name" />
              </div>
              <div class="card-content">
                <div class="content-row">{{ card.name }}
              <span class="config-badge">
                  设备配置
                </span>

                </div>
                <div class="content-row">{{ card.type }}</div>
                <div class="content-row">{{ card.status }}
               <span>
                </span>


                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
         <a-breadcrumb class="p16">
      <img
          src="@/assets/images/red_line.png"
          alt=""
          style="width: 5px; height: 18px; margin-right: 8px"
        />
      <a-breadcrumb-item class="cursor" @click="() => (flag = {})">{{
        locales.shebeimingxi
      }}</a-breadcrumb-item>
      <a-breadcrumb-item class="cursor" v-if="flag.value">{{
        flag.label
      }}</a-breadcrumb-item>
    </a-breadcrumb>

        <div class="table-part w100" v-if="!flag.value">
          <Search :searchData="searchData" @handleEvent="handleTableFilter" />
          <div class="btns flex-row-center-between">

            <div class="btn-left flex-row-center-start nowrap">
    
            <button class="btn add-device-btn" @click="() => handleEvent('add')">
            <img src="@/assets/images/plus.png" alt="新增" class="btn-icon" />
            {{ locales.xinzengshebei }}
          </button>
             
            </div>
        
          </div>
          <Table
            :refreshTableData="() => handleQuery(queryData)"
            :isLoading="tableData.isLoading"
            :columns="tableData.columns"
            :dataSource="tableData.dataSource"
            :page="tableData.page"
            :pageSize="tableData.pageSize"
            :total="tableData.total"
            :selectedRowKeys="tableData.selectedRowKeys"
            @pageChange="handlePageChange"
            @emitRowCheckboxChange="handleRowCheckboxChange"
          >
            <template #action="{ record }">
              <div class="action-buttons">
                <span class="action-btn action-btn-primary" @click="() => handleRowEvent('view', record)">
                  {{ locales.chakan }}
                </span>
                <span class="action-btn action-btn-primary" @click="() => handleRowEvent('update', record)">
                  {{ locales.xiugai }}
                </span>
                <span class="action-btn action-btn-danger" @click="() => handleRowEvent('delete', record)">
                  {{ locales.shanchu }}
                </span>
              </div>
            </template>
          </Table>
        </div>
        <Form
          v-else
          :isEdit="flag.value == 'add' || flag.value == 'update'"
          :formData="flag.value == 'view' ? viewData : formData"
          @handleEvent="handleFormEvent"
        ></Form>
      </div>
    </div>
    <a-modal
      v-model:open="openVersion"
      :title="locales.chaxunbanben"
      class="component-modal version"
    >
      <div class="modal-box flex-row-center-center nowrap">
        <button
          class="btn flex-row-center-center"
          @click="() => handleVersionQuery(1)"
        >
          查询自身版本
        </button>
      </div>
    </a-modal>
    <a-modal
      v-model:open="openImport"
      :title="locales.daorunibianqi"
      :okText="locales.tijiao"
      class="component-modal import"
      @ok="upload"
    >
      <div class="modal-box flex-row-center-start nowrap">
        <span>{{ locales.shangchuanwenjian }}：</span>
        <input type="file" @change="handleFileUpload" />
      </div>
    </a-modal>
  </div>
</template>
  
  <script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import { useStore } from 'vuex';
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const { equipmentManagerColumn } = require("@/db.js");
import {
  getDataList,
  saveOrUpdate,
  deleteComponent,
  componentUpload,
  versionQueryTask,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";

const Search = defineAsyncComponent(() =>
  import("../components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);

// 定义 props
const props = defineProps({
  viewData: {
    type: Object,
    default: () => null
  }
});

// 定义 emit
const emit = defineEmits(['handleEvent']);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
const store = useStore();
const userName = sessionStorage.getItem("userName") || "";
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

// 获取从 store 传递过来的数据
const stationViewData = computed(() => {
  return props.viewData || store.state.equipmentViewData;
});

// 设备卡片数据
const equipmentCards = ref([
  {
    name: "智能网关",
    type: "IMEI#",
    status: "正常",
    count: "1",
    unit: "台",
    image: require("@/assets/images/collector.webp")
  },
  {
    name: "优化器",
    type: "IMEI#",
    status: "正常",
    count: "30",
    unit: "个",
    image: require("@/assets/images/optimizer.webp")
  },
  {
    name: "中继器",
    type: "IMEI#",
    status: "正常",
    count: "1",
    unit: "台",
    image: require("@/assets/images/relay.webp")
  },
  {
    name: "电站组串",
    type: "",
    status: "优化器数量",
    count: "1",
    unit: "串",
    image: require("@/assets/images/group.webp")
  },
  {
    name: "逆变器",
    type: "IMEI#",
    status: "",
    count: "1",
    unit: "台",
    image: require("@/assets/images/inverter.png")
  }
]);

// 计算总设备数量
function getTotalEquipment() {
  if (!stationViewData.value?.equipmentStats) return 0;
  const stats = stationViewData.value.equipmentStats;
  return (stats.cloudTerminalNum || 0) +
         (stats.componentNum || 0) +
         (stats.inverterNum || 0) +
         (stats.relayNum || 0) +
         (stats.stringNum || 0);
}


onBeforeMount(() => {
  tableData.columns = equipmentManagerColumn;
  handleQuery();
});

const searchData = ref([
  {
    label: locales.value.zujianzuobiao,
    key: "chipId",
    value: undefined,
    type: "input",
  }
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}
async function handleQuery(data) {
  let res = await getDataList("component/queryComponentList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    chipId: data?.chipId || "",
    powerStationName: data?.powerStationName || "",
    producers: data?.producers || "",
    model: data?.model || "",
    createTimeStart: data?.createTime?.[0] || "",
    createTimeEnd: data?.createTime?.[1] || "",
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource = reModel.data;
    }
  }
}

let flag = ref({});
let viewData = ref([]);
let formData = ref([
  {
    label: locales.value.bianhao,
    key: "componentNo",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.shengchanshang,
    key: "producers",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.chuchangqianxuliehao,
    key: "serialNo",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.xinghao,
    key: "model",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.zujianzuobiao,
    key: "chipId",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.chuangjianren,
    key: "createUserName",
    value: userName,
    type: "input",
    disabled: true,
  },
]);

async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增or修改
    data.createUserName = undefined;
    if (flag.value.value == "update") data.id = tableData.selectedRowKeys[0];

    let res = await saveOrUpdate("component", data);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

// 处理行操作事件
async function handleRowEvent(type, record) {
  // 设置选中的行
  tableData.selectedRowKeys = [record.id];
  // 调用原有的事件处理函数
  await handleEvent(type);
}
async function handleEvent(type) {
  if (type == "delete") {
    if (!tableData.selectedRowKeys?.length)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  } else if (type != "add" && type != "import-file") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }

  if (type == "add") {
    // 新增
    flag.value = { label: locales.value.xinzeng, value: "add" };
    formData.value.forEach((v) => {
      if (!v.disabled) v.value = undefined;
    });
  } else if (type == "update") {
    // 修改
    flag.value = { label: locales.value.xiugai, value: "update" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    formData.value.forEach((v) => {
      v.value = curData[v.key];
    });
  } else if (type == "view") {
    // 查看
    flag.value = { label: locales.value.chakan, value: "view" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );

    viewData.value =
      formData.value
        .filter((e) => e.key != "createUserName")
        .map((v) => {
          v.value = curData[v.key];
          let { required, ...others } = v;
          return {
            ...others,
            disabled: true,
          };
        }) || [];

    let {
      softVersion,
      hardVersion,
      bootPartition,
      bomId,
      mcu,
      updateTime,
      createTimeCh,
      imei,
      createUserName,
    } = curData;
    let arr = [
      {
        label: locales.value.caijiqibiaoshi,
        key: "imei",
        value: imei,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.yingjianbanbenhao,
        key: "hardVersion",
        value: hardVersion,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.ruanjianbanbenhao,
        key: "softVersion",
        value: softVersion,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.fenqu,
        key: "bootPartition",
        value: bootPartition,
        type: "input",
        disabled: true,
      },
      {
        label: "mcu",
        key: "mcu",
        value: mcu,
        type: "input",
        disabled: true,
      },
      {
        label: "bomId",
        key: "bomId",
        value: bomId,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.gengxinshijian,
        key: "updateTime",
        value: updateTime,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.chaungjianshijian,
        key: "createTimeCh",
        value: createTimeCh,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.chuangjianren,
        key: "createUserName",
        value: createUserName,
        type: "input",
        disabled: true,
      },
    ];
    viewData.value = [...viewData.value, ...arr];
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deleteComponent({
          id: tableData.selectedRowKeys.join(","),
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "view-version") {
    // 查询版本
    openVersion.value = true;
    flag.value = {};
  } else if (type == "import-file") {
    // 导入
    openImport.value = true;
    flag.value = {};
  }
}

let openVersion = ref(false);
let openImport = ref(false);
async function handleVersionQuery(queryType) {
  let res = await versionQueryTask({
    queryType,
    id: tableData.selectedRowKeys[0],
  });
  if (res?.data?.code === 0) {
    message.success(locales.value.caozuochenggong);
    handleQuery();
  } else message.warning(locales.value.caozuoshibai);
  openVersion.value = false;
}
let selectedFile = ref(null);
function handleFileUpload(event) {
  selectedFile.value = event.target.files[0];
  if (selectedFile.value) {
    let nameArr = selectedFile.value.name.split(".") || [];
    let type = nameArr[nameArr.length - 1];
    const typeList = ["xls", "xlsx", "csv"];
    if (typeList.findIndex((e) => e == type) < 0) {
      return message.warning(locales.value.excelgeshi);
    }
  }
}
async function upload() {
  // 提交
  try {
    const formData = new FormData();
    formData.append("upfile", selectedFile.value);
    await componentUpload(formData);
    message.success(locales.value.caozuochenggong);

    openImport.value = false;
  } catch (error) {
    message.warning(locales.value.caozuoshibai);
    openImport.value = false;
  }
}
</script>
  
<style lang="less" scoped>
.equipment-manager {
    border-radius: 10px;
  .main-content {
    height: 100%;
    gap: 0;
  }

  .left-section {
    width: 50%;
    height: 100%;
    padding-right: 10px;
    padding-left: 16px;
    overflow-y: auto;

    .navigation-bar {
      margin-bottom: 20px;
      padding: 12px 16px;
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .nav-header {
        width: 100%;

        h2 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }

        .back-btn {
          background-color: #007bff;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 6px 12px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: #0056b3;
          }
        }
      }
    }

    .station-info-card {
      margin-bottom: 20px;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .station-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e9ecef;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .station-id {
          font-size: 12px;
          color: #666;
          background-color: #f8f9fa;
          padding: 4px 8px;
          border-radius: 4px;
        }
      }

      .station-details {
        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          padding: 4px 0;

          .label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
          }

          .value {
            font-size: 14px;
            color: #333;
            font-weight: 400;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .second-row {
      margin-bottom: 20px;
      gap: 16px;

      .station-image {
        flex-shrink: 0;
        width: 100px;
        height: 75px;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .grid-section {
        flex: 1;

        .grid-container {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          grid-template-rows: repeat(3, 1fr);
          gap: 8px;
          height: 150px;

          .grid-item {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px;
            transition: all 0.3s ease;

            .grid-label {
              font-size: 12px;
              color: #666;
              margin-bottom: 4px;
            }

            .grid-value {
              font-size: 16px;
              font-weight: 600;
              color: #007bff;
            }

            &:hover {
              background-color: #f8f9fa;
              border-color: #007bff;
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
            }
          }
        }
      }
    }

    .cards-section {
      .cards-row {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .equipment-card {
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        flex: 0 0 calc(50% - 6px); // 固定宽度为50%减去gap的一半
        max-width: calc(50% - 6px);

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .card-image {
          flex-shrink: 0;
          width: 60px;
          height: 60px;
          margin-right: 16px;
          border-radius: 6px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .card-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4px;

          .content-row {
            font-size: 14px;
            color: #333;
            display: flex; 
            justify-content: 
            space-between; 
            align-items: center;


            &:first-child {
              font-weight: 600;
              font-size: 15px;
              /* 保持和设备配置对齐 */
              margin-left: 0;
            }

            &:nth-child(2) {
              color: #666;
              font-size: 13px;
            }

            &:last-child {
              color: #666;
              font-size: 12px;
            }

            .config-badge {
              font-weight: 600;
              font-size: 12px;
              color: #007bff;
              border: 0.5px solid #007bff;
              border-radius: 10px;
              padding: 4px 8px;
            }
          }
        }

      }
    }
  }

  .right-section {
    width: 50%;
    height: 100%;
    padding-left: 10px;
    overflow-y: auto;

    .right-content {
      margin-bottom: 20px;
      gap: 20px;
      padding: 16px;
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .station-info-column {
        flex: 1;
        padding-right: 20px;


        .info-row {
          display: flex;
          align-items: center;

          &:last-child {
            margin-bottom: 0;
          }

          .info-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            min-width: 80px;
            margin-right: 12px;
          }

          .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 400;
            flex: 1;
          }

          .info-status {
            font-size: 12px;
            color: #52c41a;
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 12px;
            padding: 2px 8px;
            margin-left: 8px;
          }
        }
      }

      .weather-info-column {
        flex: 1;
        padding-left: 20px;

        .weather-row {
          display: flex;
          align-items: center;
          margin-top: 8px !important;
          margin-bottom: 8px !important;
          // padding: 8px ;
          padding-bottom: 10px !important ;
          &:last-child {
            margin-bottom: 0;
          }

          .weather-icon {
            width: 6px;
            height: 6px;
            margin-right: 10px;
            flex-shrink: 0;

            img {
              width: 50%;
              height: 50%;
              object-fit: cover;
            }
          }

          .weather-text {
            font-size: 13px;
            color: rgba(153, 153, 153, 1);
            font-weight: 400;
            line-height: 1.4;
          }
        }
      }
    }

    .btns {
      margin: 20px 0 12px;
    }

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 12px;

      .action-btn {
        cursor: pointer;
        font-size: 14px;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &.action-btn-primary {
          color: #1890ff;

          &:hover {
            color: #40a9ff;
            background-color: #e6f7ff;
          }
        }

        &.action-btn-danger {
          color: #ff4d4f;

          &:hover {
            color: #ff7875;
            background-color: #fff2f0;
          }
        }
      }
    }
  }
}
</style>

<style lang="less">
.component-modal {
  width: 450px !important;
  .modal-box {
    padding: 20px;
    .btn {
      width: 140px !important;
      border-radius: 2px !important;
    }
  }
  &.version {
    .ant-modal-footer {
      .ant-btn-primary {
        display: none !important;
      }
    }
  }
}
</style>