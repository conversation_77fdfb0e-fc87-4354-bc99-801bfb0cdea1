<template>
  <div class="map-wrapper wh100 plr16">
    <div class="map wh100" id="leafletMap"></div>
    <!-- 搜索模块 -->
    <div class="search-container flex-row nowrap">
      <Search
        :searchData="searchData"
        @handleEvent="(v) => handleQuery(v.data)"
      />
    </div>
    <!-- 详情弹窗 -->
    <div class="pop-wraper wh100" v-if="flag">
      <div class="pop-mask wh100" @click="flag = false"></div>
      <div class="pop">
        <div class="top flex-row">
          <img :src="curStation.stationPicture" alt="" class="images"/>
          <div class="info flex1">
            <div class="title w100 flex-row-center-between">
              <div>{{ curStation.systemName || "-" }}</div>
              <div class="status">正常</div>
            </div>
            <div class="item nowrap flex-row-center-start">
              <div class="label">电站地址：</div>
              <div class="value">
                {{ curStation.countries || ""
                }}{{
                  curStation.provinceName ? "，" + curStation.provinceName : ""
                }}{{ curStation.cityName ? "，" + curStation.cityName : ""
                }}{{
                  curStation.streetName ? "，" + curStation.streetName : ""
                }}
              </div>
            </div>
            <div class="item nowrap flex-row-center-start">
              <div class="label">创建时间：</div>
              <div class="value">{{ curStation.createTimeCh || "-" }}</div>
            </div>
            <div class="item nowrap flex-row-center-start">
              <div class="label">电站级别：</div>
              <div class="value">{{ curStation.grade || "-" }}</div>
            </div>
            <div class="item nowrap flex-row-center-start">
              <div class="label">电站功率：</div>
              <div class="value">{{ curStation.power || "-" }}kW</div>
            </div>
          </div>
        </div>
        <div class="bottom w100">
          <div class="main flex-row nowrap">
            <div class="left flex-row nowrap">
              <div class="l flex-column-center-start">
                <img src="@/assets/images/tianqi-1.png" alt="" />
                <span>阴天</span>
              </div>
              <div class="r flex-column-end-start">
                <div class="date">周六 2025-01-25</div>
                <div class="temp">-6 °C</div>
                <div class="temp-range">-6 °C～1°C</div>
                <div class="wind">风速12.34km/h</div>
              </div>
            </div>
            <div class="line"></div>
            <div class="right flex-row">
              <div class="temp-item flex-column-center-center">
                <div class="week">周日</div>
                <img src="@/assets/images/tianqi-yu.png" alt="" />
                <div class="weather">雨天</div>
                <div class="temp">6 °C～0°C</div>
              </div>
              <div class="temp-item flex-column-center-center">
                <div class="week">周一</div>
                <img src="@/assets/images/tianqi-2.png" alt="" />
                <div class="weather">晴天</div>
                <div class="temp">3 °C～1°C</div>
              </div>
              <div class="temp-item flex-column-center-center">
                <div class="week">周二</div>
                <img src="@/assets/images/tianqi-2.png" alt="" />
                <div class="weather">晴天</div>
                <div class="temp">3 °C～1°C</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  onMounted,
  onUnmounted,
  ref,
  defineAsyncComponent,
  onBeforeMount,
  nextTick,
  watch,
  computed,
} from "vue";
import L from "leaflet";
import "leaflet-boundary-canvas";
require("./config/tileLayer.baidu.js");
import chinaMap from "./config/map.json";
import markerIcon from "./config/map.png";
import { getDataList } from "@/api/list";

const Search = defineAsyncComponent(() =>
  import("@/views/components/Search/index.vue")
);

let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

let curStation = ref({});
let station_list = ref([]);

let map;
let layers = [];
let group;
let zoom;

let flag = ref(false);

function initMap() {
  map && map.remove();
  map = L.map("leafletMap", {
    minZoom: 4,
    maxZoom: 18,
    center: [
      station_list.value[0]?.latitude || "39.910925",
      station_list.value[0]?.longitude || "116.413384",
    ],
    zoom: 9,
    zoomControl: false,
    attributionControl: false,
    crs: L.CRS.Baidu,
    rotate: true,
    bearing: 200,
  });
  changePic();
  L.tileLayer.baidu({ layer: "vec" }).addTo(map);
  let jhLine = L.geoJSON(chinaMap, {
    style: {
      color: "rgba(76, 82, 77, 0.5)",
      weight: 3,
      fillColor: "",
      fillOpacity: 0.00001,
    },
  });
  jhLine.addTo(map);
}

function addPoint() {
  group && group.clearLayers();
  layers = [];
  station_list.value.forEach((item) => {
    //判断选中数据
    if (item.latitude && item.longitude) {
      let icon = L.marker([item.latitude, item.longitude], {
        icon: L.icon({
          className: "my-icon",
          iconUrl: markerIcon,
          iconSize: L.point(35, 40),
        }),
        position: `${item.latitude}-${item.longitude}`,
        type: "icon",
        size: [48, 56],
        zIndex: 9999,
      }).on("click", () => {
        flag.value = true;
        curStation.value = item;
      });
      layers.push(icon);
    }
  });
}

//改变图形比例
function changePic() {
  addPoint();
  group = L.layerGroup(layers);
  map.addLayer(group);
}

const gradeOpt = [
  { label: "微型", value: 1 },
  { label: "小型", value: 2 },
  { label: "中型", value: 3 },
  { label: "大型", value: 4 },
  { label: "特大型", value: 5 },
];
const searchData = ref([
  {
    label: locales.value.dianzhanmingcheng,
    key: "systemName",
    value: undefined,
    type: "input",
  },
  {
    label: locales.value.dianzhanjibie,
    key: "grade",
    value: undefined,
    type: "select",
    options: gradeOpt,
  },
  {
    label: locales.value.diqu,
    key: "area",
    value: [],
    type: "cascader",
  },
  {
    label: locales.value.jiedao,
    key: "streetName",
    value: undefined,
    type: "input",
    width: 30,
  },
]);

// 获取电站信息
async function handleQuery(data) {
  let res = await getDataList("powerstation/queryPowerStationList.web", {
    systemName: data?.systemName || "",
    countriesId: data?.area[0] || "",
    province: data?.area[1] || "",
    cityId: data?.area[2] || "",
    streetName: data?.streetName || "",
    grade: data?.grade || "",
  });
  if (res?.data) {
    let { reModel, code } = res.data;
    if (code === 0 && reModel) {
      station_list.value =
        reModel.data
          ?.filter((v) => v.latitude && v.longitude)
          .map((e) => {
            return {
              ...e,
              grade: gradeOpt.find((v) => v.value == e.grade)?.label || "",
              stationPicture:
                e.stationPicture || require("@/assets/images/station.svg"),
            };
          }) || [];
    }
  }
}

watch(
  () => station_list.value,
  () => {
    nextTick(() => {
      if (map) changePic();
    });
  },
  { deep: true }
);

onBeforeMount(() => {
  handleQuery();
});
onMounted(() => {
  const interval = setInterval(() => {
    if (document.getElementById("leafletMap")) {
      clearInterval(interval);
      initMap();
      map.on({ zoomend: changePic }, this);
    }
  }, 100);
});
onUnmounted(() => {
  map?.remove();
});
</script>

<style lang='less' scoped>
.map-wrapper {
  position: relative;
  .map {
    background: transparent !important;

    // filter: brightness(110%) drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5));
    .leaflet-pane {
      transform: rotateX(20deg);
    }

    .leaflet-map-pane {
      .leaflet-overlay-pane {
        path {
          // filter: brightness(110%) drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(0px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5)) drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5)) drop-shadow(-1px 0px 0px rgba(13, 62, 28, 0.5));
          filter: brightness(110%)
            drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.2))
            drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.2))
            drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.5))
            drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.5))
            drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.2))
            drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.2))
            drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5))
            drop-shadow(1px 0px 0px rgba(76, 82, 77, 0.5))
            drop-shadow(1px 0px 0px rgba(13, 62, 28, 0.2));
        }
      }
    }
  }
  .my-icon {
    z-index: 999 !important;
  }
  .pop-wraper {
    position: absolute;
    left: 0;
    top: 0;
    .pop-mask {
      position: absolute;
      left: 0;
      top: 0;
      background-color: transparent;
      z-index: 1001;
    }
    .pop {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
       width: 600px;
      height: 430px;
      opacity: 1;
      border-radius: 30px;
      background: rgba(255, 255, 255, 0.6);
      border: 3px solid rgba(255, 255, 255, 1);
      box-shadow: 2px 2px 30px  rgba(0, 0, 0, 0.2);
      backdrop-filter: blur(30px);
      z-index: 1002;
      padding: 54px 57px;
      .top {
        border-bottom:  1px solid rgba(212, 212, 212, 1);
        .images {
          width: 180px;
          height: 180px;  
        }
        .info {
          padding: 15px 0 2px 24px;
          .title {
            .status{
                width: 58px;
                height: 27px;
                border-radius: 13px;
                background: rgba(115, 199, 100, 0.12);
                font-size: 14px;
                line-height: 27px;
                color: rgba(73, 176, 45, 1);
                text-align: center;
              }
          }
          .item {
            margin-top: 8px;
            .label {
              font-size: 14px;
              font-weight: 500;
              line-height: 22px;
              color: #0d0c12;
            }
            .value {
              font-size: 14px;
              line-height: 22px;
              color: #0d0c12;
            }
          }
        }
      }
      .bottom {
        margin-top: 35px;
        .main {
          .left {
            .l {
              margin-right: 12px;
              > img {
                width: 50px;
                height: 44px;
                margin-bottom: 20px;
              }
              > span {
                font-size: 14px;
                font-weight: 700;
                line-height: 22px;
                color: #383838;
                text-align: center;
              }
            }
            .r {
              .date {
                font-size: 14px;
                font-weight: 500;
                line-height: 20px;
                color: #383838;
              }
              .temp {
                margin: 2px 0 4px;
                font-size: 24px;
                font-weight: 500;
                line-height: 33px;
                color: #165dff;
              }
              .temp-range {
                font-size: 14px;
                line-height: 22px;
                color: #808080;
              }
              .wind {
                font-size: 14px;
                line-height: 22px;
                color: #808080;
              }
            }
          }
          .line {
            width: 1px;
            height: 98px;
            margin: 7px 26px 0;
            background-color: #e5e5e5;
          }
          .right {
            .temp-item {
              width: 90px;
              height: 120px;
              margin-left: 10px;
              border-radius: 6px;
              background: #f7f7f7;
              .week {
                font-size: 14px;
                line-height: 20px;
                color: #383838;
                margin-bottom: 8px;
              }
              > img {
                width: 34px;
                height: 34px;
              }
              .weather {
                font-size: 12px;
                line-height: 17px;
                color: #808080;
                margin: 3px 0 6px;
              }
              .temp {
                font-size: 12px;
                line-height: 17px;
                color: #383838;
              }
              &:first-child {
                margin-left: 0;
              }
            }
          }
        }
      }
    }
  }
  .search-container {
    position: absolute;
    top: 10px;
    left: 25px;
    z-index: 1000;
    width: calc(100% - 50px);
    padding: 20px 16px 0;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.6);
    border: 2px solid rgba(255, 255, 255, 1);
    box-shadow: 2px 2px 30px  rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(30px);
  }
}
</style>

<style lang="less">
.ant-select-dropdown {
  padding: 0 !important;
  border-radius: 0 !important;
  .ant-select-item {
    height: 42px !important;
    padding: 10px 12px !important;
    border-radius: 0 !important;
    .ant-select-item-option-content {
      font-size: 16px;
      color: #383838;
    }
    &.ant-select-item-option-selected {
      background-color: #e9f6fe;
      .ant-select-item-option-content {
        font-weight: 500;
      }
    }
  }
}
</style>
