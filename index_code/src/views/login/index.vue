<template>
  <div class="login-view wh100">
    <div class="logo">
      <div class="text_big">
        <span class="white">HI-MO</span>
        <span class="red">X10</span>
      </div>
      <div class="text_small">
        {{ locale?.titleText }}
      </div>
    </div>
    <div class="lang">
      
       <a-dropdown>
        <a class="ant-dropdown-link" @click.prevent>
          <img src="../../assets/images/Lang.png" alt="">
          <DownOutlined />
        </a>
        <template #overlay>
          <a-menu  @click="dropdownClick">
            <a-menu-item v-for="(item,index) in languageOpt" :key="index">
              {{ item.label }}
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <img src="../../assets/images/chen.png" alt="" @click="changeCNEN">
     
    </div>
    <div class="login-modal">
      <div class="title">
        <span class="red">Hello！</span>
        <span class="white">  {{ locale?.welcomeLogin }}</span>
        
      </div>
      <div class="form_inner">
        <a-form
        ref="formRef"
        :model="formState"
        :label-col="{ span: 0 }"
        :wrapper-col="{ span: 24 }"
      >
        <a-form-item
          name="username"
          :rules="[
            {
              required: true,
              message: locale?.usernameCheck,
              trigger: 'focus',
            },
          ]"
        >
          <a-input
            v-model:value="formState.username"
            :placeholder="locale?.usernamePlaceholder"
            @keyup.enter="handleLogin"
          />
        </a-form-item>

        <a-form-item
          name="password"
          :rules="[
            { required: true, message: locale?.pswCheck, trigger: 'focus' },
          ]"
        >
          <a-input-password
            v-model:value="formState.password"
            :placeholder="locale?.pswPlaceholder"
            @keyup.enter="handleLogin"
          />
        </a-form-item>
        <!-- <a-form-item name="language">
          <a-select v-model:value="formState.language" :options="languageOpt" />
        </a-form-item> -->
          <a-form-item name="remember">
          <div class="text">
            <!-- <div class="text_item">
              <a-radio v-model:checked="rememberUAW">
                {{ locale?.rememberUAW }}
              </a-radio>
            </div> -->
            <!-- <div class="text_item red"> {{ locale?.forgetPs }}？</div> -->
          </div>
        </a-form-item>
         <a-form-item name="agreen">
          <div class="text">
            <div class="text_item">
              <a-radio v-model:checked="agreenSAP">
                {{ locale?.agreenSAP }} <span class="blue" @click="openAgreementUrl" style="cursor:pointer;">{{ locale?.agreenSAP1 }}</span> 
                {{ locale?.agreenSAP2 }} <span class="blue" @click="openPrivacyUrl" style="cursor:pointer;">{{ locale?.agreenSAP3 }}</span>
              </a-radio>
            </div>
          </div>
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 0, span: 24 }">
          <a-button class="mt16" @click="handleLogin">{{
            locale?.login
          }}</a-button>
        </a-form-item>
      </a-form>
      <div class="footer_inner">
        <div class="footer_item">
          <!-- <span>{{
            locale?.nAccount
          }}？</span>
          <span class="red">{{
            locale?.registr
          }} →</span> -->
        </div>
         <div class="footer_item mt20">
          <!-- <span class="gary">{{
            locale?.lw
          }}？</span> -->
          <!-- <span class="gary">{{
            locale?.admin_line
          }}</span> -->
        </div>
      </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { login } from "@/api/list.js";
import { useRouter } from "vue-router";
import { message } from "ant-design-vue";

import locales from "@/locales.json";
const router = useRouter();
const formState = ref({
  username: "",
  password: "",
  language: "zh",
  remember: true,
});
let formRef = ref(null);
const rememberUAW = ref(false);
const agreenSAP = ref(false);
const languageOpt = [
  { label: "中文", value: "zh" },
  { label: "English", value: "en" },
  { label: "عربي ،", value: "ar" }, // 阿拉伯语
  { label: "日本語", value: "ja" },
  { label: "Français", value: "fr" }, // 法语
  { label: "Español", value: "es" }, // 西班牙语
];

let locale = computed(() => locales[formState.value.language || "zh"]);

let emit = defineEmits(["loginsuc"]);
const changeCNEN = () => {
  if(formState.value.language == 'zh') {
    formState.value.language = 'en'
  } else {
     formState.value.language = 'zh'
  }
}
const dropdownClick = ({ key }) => {
  console.log(key,"key")
  // formState.value.language = languageOpt[key].value
}
function handleLogin() {
  formRef.value
    .validateFields()
    .then(async () => {
      // checkFlagif(agreenSAP){
      //   console.log('登录成功');
      if(!agreenSAP.value) {
        message.error("请先阅读服务协议和隐私政策")
        return
      }
      // 校验成功-登录
      let res = await login({
        userId: formState.value.username,
        password: formState.value.password,
      });
      console.log(res, "====1111===");
      //  debugger;
      if (res?.data?.code === 0) {
        sessionStorage.setItem("userName", formState.value.username);
        sessionStorage.setItem(
          "locales",
          JSON.stringify(locales[formState.value.language || "zh"])
        );
        emit("loginsuc", locales[formState.value.language || "zh"], formState.value.username); // 传递用户名
        await router.push("/");
      } else {
        message.warning(locale.value.loginCheck);
        sessionStorage.removeItem("userName");
        sessionStorage.removeItem("locales");
      }

      // }else{
      //   alert('请读完隐私信息')
      //   console.log('请读完隐私信息');
      // }

    })
    .catch((errors) => {
      // 校验失败，可以根据需要处理错误信息。
      console.log(errors, "=======");
      return;
    });
}
const openAgreementUrl = () => {
  window.open('http://download.yimeixu.com:6273/ymx/cn/ymx_user_agreement_cn.html', '_blank');
};
const openPrivacyUrl = () => {
  window.open('http://download.yimeixu.com:6273/ymx/cn/ymx_privacy_policy_cn.html', '_blank');
};
</script>

<style lang="less" scoped>
.login-view {
  position: relative;
  background-size: cover;
  background-image: url("../../assets/images/login-bg-2.png");
  // animation: backgroundFade 6s infinite;

  // @keyframes backgroundFade {
  //   0%,
  //   100% {
  //     background-image: url("../../assets/images/login-bg-1.png");
  //   }
  //   50% {
  //     background-image: url("../../assets/images/login-bg-2.png");
  //   }
  // }
  .logo{
    position: absolute;
    top: 195px;
    left: 243px;
    width: 400px;
    height: 100px;
    .text_big{
      width: 100%;
      height: 42px;
      text-align: left;
      line-height: 42px;
      font-size:50px;
      font-weight: 600;
      color: #fff;
      display: flex;
      align-items: center;
      .red{
        font-size: 28px;
        display: inline-block;
        padding: 0px 10px;
        background: rgba(230, 0, 18, 1);
        margin-left: 14px;
      }
    }
    .text_small{
      width: 100%;
      height: 50px;
      margin-top: 20px;
      font-size: 36px;
      color: #fff;
    }
  }
  .lang{
    position: absolute;
    top: 38px;
    right: 78px;
    width: 80px;
    height: 28px;
    display: flex;
    justify-content: space-between;
    img{
      width: 28px;
      height: 28px;
      cursor: pointer;
    }
  }
  .login-modal {
    position: absolute;
    margin: auto;
    right: 12%; 
    top: 0;
    bottom: 0;
    padding-bottom: 45px;
    width: 440px;
    min-height: 520px;
    display: table;
    border-radius: 30px;
    background:rgba(99, 99, 99, 0.2);
    border: 2px solid rgba(217, 217, 217, 0.6);
    box-shadow: 2px 4px 8px  rgba(0, 0, 0, 0.25);
    .title {
      font-size: 24px;
      text-align: center;
      margin-top: 62px;
      .red{
        color: rgba(230, 0, 18, 1);
      }
      .white{
        color: #fff;
      }
    }
    .form_inner{
      width: 90%;
      display: table;
      margin: 22px auto 0 auto;
      .text{
         width: 90%;
         margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .text_item{
          font-size: 14px;
          font-weight: 500;
        }
        .red{
          color: rgba(230, 0, 18, 1);
          cursor: pointer;
        }
        .blue{
          color: rgba(0, 132, 255, 1);
        }
      }
      .footer_inner{
        width: 90%;
        margin: 0 auto;
        .footer_item{
          font-size: 14px;
          color: #fff;
           .red{
              color: rgba(230, 0, 18, 1);
              cursor: pointer;
            }
            .gary{
              color: rgba(128, 128, 128, 1);
              font-size: 14px;
            }
        }
        .mt20{
          margin-top: 20px;
        }
      }
    }
    :deep(.ant-form) {
      width: 100%;
      .ant-form-item-control-input-content {
        > .ant-input,
        .ant-input-password,
        .ant-select-selector {
          width: 100%;
          height:50px;
          font-size: 20px;
          color: #000;
          border-radius: 30px;
          background: rgba(255, 255, 255, 1);
          overflow: hidden;
            padding: 4px 34px;
          &::placeholder {
            font-size: 20px;
            color: rgba(204, 204, 204, 1);
          }
          &:focus,
          &.ant-input-affix-wrapper-focused {
            -moz-box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1) inset;
            -webkit-box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1) inset;
            box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1) inset;
            border: 1px solid rgba(255, 255, 255, 0.15);
          }
          .ant-select-selection-item {
            line-height: 50px !important;
          }
          > .ant-input {
            padding: 4px 34px;
            font-size: 20px;
            color: #000;
            border: none;
            background: transparent;
            &::placeholder {
              font-size: 20px;
              color: #000;
            }
          }
          &.ant-input-affix-wrapper {
            padding: 0;
          }
          .ant-input-suffix {
            display: none;
            > .anticon {
              color: #000;
            }
          }
        }
        > button {
          width: 90%;
          height: 48px;
          border-radius: 50px;
          background: linear-gradient(180deg, rgba(230, 0, 18, 1) 0%, rgba(239, 88, 100, 1) 100%);
          box-shadow: 0px 2.85px 5.7px  rgba(0, 0, 0, 0.25);
          margin: 0 auto;
          display: block;
          font-size: 24px;
          color: #fff;
        }
      }
      .ant-radio-wrapper{
        color: #fff;
        font-size: 14px;
      }
      .ant-radio-inner{
        width: 20px;
        height: 20px;
            border-color: #fff;
        background: transparent;
      }
    }
  }
}
</style>
