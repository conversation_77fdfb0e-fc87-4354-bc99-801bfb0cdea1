<template>
  <div class="inverter wh100 plr16">
  
    <div class="table-part w100" v-if="!flag.value">
      <Search :searchData="searchData" @handleEvent="handleTableFilter" />
      <div class="btns flex-row-center-between">
        <div class="btn-left flex-row-center-start nowrap">
          <div class="btn" @click="() => handleEvent('select-group')">
            <span>{{ locales.xuanzezuchuan }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('select-cloud')"
          >
            <span>{{ locales.xuanzecaijiqi }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('view-device')"
          >
            <span>{{ locales.chakanshebei }}</span>
          </div>
          <div
            class="btn ml16" style="display: flex;justify-content: center;align-items: center;"
            @click="() => handleEvent('import-file')"
          >
            <span>{{ locales.daoru }}</span>
          </div>
        </div>
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('add')">
            {{ locales.xinzengshebei }}
          </button>
          <button
            class="btn btn-delete ml16"
            @click="() => handleEvent('delete')"
          >
            {{ locales.shanchu }}
          </button>
        </div>
      </div>
      <Table
        :refreshTableData="() => handleQuery(queryData)"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      >
        <template #action="{ record }">
          <div class="action-buttons">
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('view', record)">
              {{ locales.chakan }}
            </span>
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('update', record)">
              {{ locales.xiugai }}
            </span>
            <span class="action-btn action-btn-danger" @click="() => handleRowEvent('delete', record)">
              {{ locales.shanchu }}
            </span>
          </div>
        </template>
      </Table>
    </div>
    <DeepTable
      v-else-if="flag.value == 'deep'"
      :btns="btns"
      :tabs="tabs"
      :api="curApi"
      :params="params"
      :refresh="refresh"
      :columns="deepColumn"
      @handleTabChange="handleTabChange"
      @handleBtnClick="handleBtnClick"
    />
    <Form
      v-else
      :isEdit="flag.value == 'add' || flag.value == 'update'"
      :formData="flag.value == 'view' ? viewData : formData"
      @handleEvent="handleFormEvent"
    ></Form>
    <a-modal
      v-model:open="openImport"
      :title="locales.daorunibianqi"
      :okText="locales.tijiao"
      class="inverter-modal import"
      @ok="upload"
    >
      <div class="modal-box flex-row-center-start nowrap">
        <span>{{ locales.shangchuanwenjian }}：</span>
        <input type="file" @change="handleFileUpload" />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent, onBeforeMount, computed } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const {
  inverterColumn,
  selectGroupColumn,
  selectCloudColumn,
} = require("@/db.js");
import {
  getDataList,
  saveOrUpdate,
  deleteInverter,
  queryDicList,
  inverterUpload,
  selectCloudTerminal,
  queryComponentGroupList,
  inverterselectOrMoveComponentGroup,
  inverterselectOrMoveCloud,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";

const Search = defineAsyncComponent(() =>
  import("../components/Search/index.vue")
);
const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);
const DeepTable = defineAsyncComponent(() =>
  import("@/views/components/DeepTable/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
const userName = sessionStorage.getItem("userName") || "";
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

onBeforeMount(async () => {
  tableData.columns = inverterColumn;
  await getControllerOpts();
  await handleQuery();
});

let flag = ref({});
let viewData = ref([]);
let controllerOpts = ref([]);
const searchData = ref([
  {
    label: locales.value.lurushijan,
    key: "createTime",
    value: undefined,
    type: "rangeTime",
  },
  {
    label: locales.value.shengchanshang,
    key: "producers",
    value: undefined,
    type: "input",
  },
]);
let formData = ref([
  {
    label: locales.value.name,
    key: "inverterName",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.shengchanshang,
    key: "producers",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.edinggonglv,
    key: "power",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.xinghao,
    key: "model",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.xuliehao,
    key: "serialNo",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.kongzhiqi,
    key: "controller",
    value: undefined,
    type: "select",
    options: controllerOpts,
    required: true,
  },
  {
    label: locales.value.nibianqibiaoshi,
    key: "chipId",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.chuangjianren,
    key: "createUserName",
    value: userName,
    type: "input",
    disabled: true,
  },
]);

let queryData = ref(null);
async function handleTableFilter(v) {
  queryData.value = v.data;
  tableData.page = 1;
  if (v.key == "search") {
    handleQuery(v.data);
  } else if (v.key == "reset") {
    handleQuery(v.data);
  }
}
async function handleQuery(data) {
  let res = await getDataList("inverter/queryInverterList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
    producers: data?.producers || "",
    createTimeStart: data?.createTime?.[0] || "",
    createTimeEnd: data?.createTime?.[1] || "",
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource =
        reModel.data?.map((e) => {
          return {
            ...e,
            controller:
              controllerOpts.value.find((v) => v.value == e.controller) || "",
          };
        }) || [];
    }
  }
}

async function getControllerOpts() {
  let res = await queryDicList("controller");
  if (res?.data?.code === 0) {
    controllerOpts.value =
      res.data.reModel?.map((e) => {
        return {
          label: e.dicValueLabel,
          value: e.dicValueId,
        };
      }) || [];
  }
}

async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增or修改
    data.createUserName = undefined;
    data.id = flag.value.value == "update" ? tableData.selectedRowKeys[0] : "";
    data.picture = "";
    let res = await saveOrUpdate("inverter", data);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

// 处理行操作事件
async function handleRowEvent(type, record) {
  // 设置选中的行
  tableData.selectedRowKeys = [record.id];
  // 调用原有的事件处理函数
  await handleEvent(type);
}

let openImport = ref(false);
async function handleEvent(type) {
  if (type == "delete") {
    if (!tableData.selectedRowKeys?.length)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  } else if (type != "add" && type != "import-file") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }

  if (type != "view-device") tabs.value = [];

  if (type == "add") {
    // 新增
    flag.value = { label: locales.value.xinzeng, value: "add" };
    formData.value.forEach((v) => {
      if (!v.disabled) v.value = undefined;
    });
  } else if (type == "update") {
    // 修改
    flag.value = { label: locales.value.xiugai, value: "update" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    formData.value.forEach((v) => {
      v.value = curData[v.key];
    });
  } else if (type == "view") {
    // 查看
    flag.value = { label: locales.value.chakan, value: "view" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );

    viewData.value =
      formData.value.map((v) => {
        v.value = curData[v.key];
        let { required, ...others } = v;
        return {
          ...others,
          disabled: true,
        };
      }) || [];

    let arr = [
      {
        label: locales.value.chaungjianshijian,
        key: "createTimeCh",
        value: curData.createTimeCh,
        type: "input",
        disabled: true,
      },
    ];
    viewData.value = [...viewData.value, ...arr];
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deleteInverter({
          id: tableData.selectedRowKeys.join(","),
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "select-group") {
    // 选择组串
    flag.value = { label: locales.value.xuanzezuchuan, value: "deep" };
    curApi.value = queryComponentGroupList;
    params.value = {
      inverterFlag: "selectComponentGroup",
      inverterId: tableData.selectedRowKeys[0],
    };
    deepColumn.value = selectGroupColumn;
    btns.value = [
      { label: locales.value.queren, value: "group-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "select-cloud") {
    // 选择采集器
    flag.value = { label: locales.value.xuanzecaijiqi, value: "deep" };
    curApi.value = selectCloudTerminal;
    params.value = {
      selectInverter: "selectInverter",
      inverterId: "",
    };
    deepColumn.value = selectCloudColumn;
    btns.value = [
      { label: locales.value.queren, value: "cloud-confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
  } else if (type == "view-device") {
    // 查看设备
    flag.value = { label: locales.value.chakanshebei, value: "deep" };
    curApi.value = selectCloudTerminal;
    params.value = {
      selectInverter: "",
      inverterId: tableData.selectedRowKeys[0],
    };
    deepColumn.value = selectCloudColumn;
    btns.value = [
      { label: locales.value.yichu, value: "cloud-remove" },
      { label: locales.value.fanhui, value: "back" },
    ];
    tabs.value = [
      { label: "采集器", value: 1 },
      { label: "组串", value: 2 },
    ];
  } else if (type == "import-file") {
    // 导入
    openImport.value = true;
    flag.value = {};
  }
}

let btns = ref([]);
let tabs = ref([]);
let curApi = ref(null);
let params = ref({});
let deepColumn = ref([]);
let refresh = ref(false);
function handleBtnClick({ type, value }) {
  if (refresh.value) refresh.value = false;
  if (type == "back") flag.value = {};
  else {
    if (value?.length) {
      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = null;
          if (type == "group-confirm" || type == "group-remove") {
            let data =
              type == "group-confirm"
                ? {
                    id: value.join(","),
                    inverterFlag: "selectComponentGroup",
                    inverterId: tableData.selectedRowKeys[0],
                  }
                : {
                    id: value.join(","),
                    inverterFlag: "detailComponentGroup",
                  };
            res = await inverterselectOrMoveComponentGroup(data);
          } else if (type == "cloud-confirm" || type == "cloud-remove") {
            let data =
              type == "cloud-confirm"
                ? {
                    id: value.join(","),
                    inverterId: tableData.selectedRowKeys[0],
                  }
                : {
                    id: value.join(","),
                    inverterId: "",
                  };
            res = await inverterselectOrMoveCloud(data);
          }
          if (res.data.code === 0) {
            message.success(locales.value.caozuochenggong);
            refresh.value = true;
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    } else message.info(locales.value.zhengquexuanzecaozuoxiang);
  }
}
function handleTabChange(tab) {
  deepColumn.value = tab == 1 ? selectCloudColumn : selectGroupColumn;
  btns.value = [
    {
      label: locales.value.yichu,
      value: tab == 1 ? "cloud-remove" : "group-remove",
    },
    { label: locales.value.fanhui, value: "back" },
  ];
  params.value =
    tab == 1
      ? {
          selectInverter: "",
          inverterId: tableData.selectedRowKeys[0],
        }
      : {
          inverterFlag: "detailComponentGroup",
          inverterId: tableData.selectedRowKeys[0],
        };
  curApi.value = tab == 1 ? selectCloudTerminal : queryComponentGroupList;
}
let selectedFile = ref(null);
function handleFileUpload(event) {
  selectedFile.value = event.target.files[0];
  if (selectedFile.value) {
    let nameArr = selectedFile.value.name.split(".") || [];
    let type = nameArr[nameArr.length - 1];
    const typeList = ["xls", "xlsx", "csv"];
    if (typeList.findIndex((e) => e == type) < 0) {
      return message.warning(locales.value.excelgeshi);
    }
  }
}
async function upload() {
  // 提交
  try {
    const formData = new FormData();
    formData.append("upfile", selectedFile.value);
    await inverterUpload(formData);
    message.success(locales.value.caozuochenggong);

    openImport.value = false;
  } catch (error) {
    message.warning(locales.value.caozuoshibai);
    openImport.value = false;
  }
}
</script>

<style lang="less" scoped>
.inverter {
  .btns {
    margin: 20px 0 12px;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;

    .action-btn {
      cursor: pointer;
      font-size: 14px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &.action-btn-primary {
        color: #1890ff;

        &:hover {
          color: #40a9ff;
          background-color: #e6f7ff;
        }
      }

      &.action-btn-danger {
        color: #ff4d4f;

        &:hover {
          color: #ff7875;
          background-color: #fff2f0;
        }
      }
    }
  }
}
</style>

<style lang="less">
.inverter-modal {
  width: 450px !important;
  .modal-box {
    padding: 20px;
    .btn {
      width: 140px !important;
      border-radius: 2px !important;
    }
  }
}
</style>