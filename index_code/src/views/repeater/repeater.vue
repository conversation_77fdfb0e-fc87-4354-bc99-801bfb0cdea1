<template>
  <div class="repeater wh100 plr16">

    <div class="table-part w100" v-if="!flag.value">
      <div class="btns flex-row-center-between">
        <div class="btn-left flex-row-center-start nowrap">
          <div
            class="btn"
            @click="() => handleEvent('select-component')"
          >
            <span>{{ locales.xuanzezujian }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('view-component')"
          >
            <span>{{ locales.chakanzujian }}</span>
          </div>
          <div
            class="btn ml16"
            @click="() => handleEvent('view-version')"
          >
            <span>{{ locales.chaxunbanben }}</span>
          </div>
        </div>
        <div class="btn-right flex-row-center-start nowrap">
          <button class="btn ml16" @click="() => handleEvent('add')">
            {{ locales.xinzengshebei }}
          </button>
          <button
            class="btn btn-delete ml16"
            @click="() => handleEvent('delete')"
          >
            {{ locales.shanchu }}
          </button>
        </div>
      </div>
      <Table
        :refreshTableData="handleQuery"
        :isLoading="tableData.isLoading"
        :columns="tableData.columns"
        :dataSource="tableData.dataSource"
        :page="tableData.page"
        :pageSize="tableData.pageSize"
        :total="tableData.total"
        :selectedRowKeys="tableData.selectedRowKeys"
        @pageChange="handlePageChange"
        @emitRowCheckboxChange="handleRowCheckboxChange"
      >
        <template #action="{ record }">
          <div class="action-buttons">
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('view', record)">
              {{ locales.chakan }}
            </span>
            <span class="action-btn action-btn-primary" @click="() => handleRowEvent('update', record)">
              {{ locales.xiugai }}
            </span>
            <span class="action-btn action-btn-danger" @click="() => handleRowEvent('delete', record)">
              {{ locales.shanchu }}
            </span>
          </div>
        </template>
      </Table>
    </div>
    <DeepTable
      v-else-if="flag.value == 'deep'"
      :btns="btns"
      :api="curApi"
      :params="params"
      :refresh="refresh"
      :columns="deepColumn"
      :searchData="searchDeepData"
      @handleBtnClick="handleBtnClick"
    />
    <Form
      v-else
      :isEdit="flag.value == 'add' || flag.value == 'update'"
      :formData="flag.value == 'view' ? viewData : formData"
      @handleEvent="handleFormEvent"
    ></Form>
    <a-modal
      v-model:open="openVersion"
      :title="locales.chaxunbanben"
      class="relay-modal"
    >
      <div class="modal-box flex-row-center-center nowrap">
        <button
          class="btn flex-row-center-center"
          @click="() => handleVersionQuery(1)"
        >
          查询自身版本
        </button>
        <button
          class="btn ml16 flex-row-center-center"
          @click="() => handleVersionQuery(2)"
        >
          查询所属组件版本
        </button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { defineAsyncComponent, onBeforeMount, ref, computed } from "vue";
import tableMixin from "@/mixins/table.js";
delete require.cache[require.resolve("@/db.js")];
const { repeaterColumn, viewComponentColumn } = require("@/db.js");
import {
  getDataList,
  saveOrUpdate,
  deleteRelay,
  relayQueryComponent,
  relayChangeComponent,
  versionQueryTask,
} from "@/api/list";
import { message, Modal } from "ant-design-vue";

const Table = defineAsyncComponent(() =>
  import("../components/Table/index.vue")
);
const Form = defineAsyncComponent(() =>
  import("@/views/components/Form/index.vue")
);
const DeepTable = defineAsyncComponent(() =>
  import("@/views/components/DeepTable/index.vue")
);

let { tableData, handlePageChange, handleRowCheckboxChange } = tableMixin();
let locales = computed(
  () => JSON.parse(sessionStorage.getItem("locales")) || {}
);

onBeforeMount(() => {
  tableData.columns = repeaterColumn;
  handleQuery();
});

async function handleQuery() {
  let res = await getDataList("relay/queryRelayList.web", {
    pageNo: tableData.page,
    pageSize: tableData.pageSize,
  });
  tableData.isLoading = false;
  if (res?.data) {
    let { reModel, code, count } = res.data;
    if (code === 0 && reModel) {
      tableData.total = count;
      tableData.dataSource = reModel.data;
    }
  }
}

let flag = ref({});
let formData = ref([
  {
    label: locales.value.zhongjiqimingcheng,
    key: "relayName",
    value: undefined,
    type: "input",
    required: true,
  },
  {
    label: locales.value.zhongjiqibiaoshi,
    key: "relayId",
    value: undefined,
    type: "input",
  },
]);

let viewData = ref([]);
async function handleFormEvent({ key, data }) {
  if (key == "save") {
    // 新增or修改
    if (flag.value.value == "update") data.id = tableData.selectedRowKeys[0];

    let res = await saveOrUpdate("relay", data);
    if (res?.data?.code === 0) message.success(locales.value.caozuochenggong);
    else message.info(locales.value.caozuoshibai);

    flag.value = {};
    handleQuery();
  } else {
    // 返回上一级
    flag.value = {};
  }
}

// 处理行操作事件
async function handleRowEvent(type, record) {
  // 设置选中的行
  tableData.selectedRowKeys = [record.id];
  // 调用原有的事件处理函数
  await handleEvent(type);
}
let openVersion = ref(false);
async function handleEvent(type) {
  if (type == "delete") {
    if (!tableData.selectedRowKeys?.length)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  } else if (type != "add") {
    if (tableData.selectedRowKeys?.length != 1)
      return message.info(locales.value.zhengquexuanzecaozuoxiang);
  }

  if (type == "add") {
    // 新增
    flag.value = { label: locales.value.xinzeng, value: "add" };
    formData.value.forEach((v) => {
      if (!v.disabled) v.value = undefined;
    });
  } else if (type == "update") {
    // 修改
    flag.value = { label: locales.value.xiugai, value: "update" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    formData.value.forEach((v) => {
      v.value = curData[v.key];
    });
  } else if (type == "view") {
    // 查看
    flag.value = { label: locales.value.chakan, value: "view" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );

    viewData.value =
      formData.value.map((v) => {
        v.value = curData[v.key];
        let { required, ...others } = v;
        return {
          ...others,
          disabled: true,
        };
      }) || [];

    let {
      softVersion,
      hardVersion,
      bootPartition,
      bomId,
      mcu,
      updateTime,
      createTime,
      cloudId,
      powerStationId,
    } = curData;
    let arr = [
      {
        label: locales.value.yingjianbanbenhao,
        key: "hardVersion",
        value: hardVersion,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.ruanjianbanbenhao,
        key: "softVersion",
        value: softVersion,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.fenqu,
        key: "bootPartition",
        value: bootPartition,
        type: "input",
        disabled: true,
      },
      {
        label: "mcu",
        key: "mcu",
        value: mcu,
        type: "input",
        disabled: true,
      },
      {
        label: "bomId",
        key: "bomId",
        value: bomId,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.gengxinshijian,
        key: "updateTime",
        value: updateTime,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.chaungjianshijian,
        key: "createTime",
        value: createTime,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.dianzhanid,
        key: "powerStationId",
        value: powerStationId,
        type: "input",
        disabled: true,
      },
      {
        label: locales.value.caijiqiid,
        key: "cloudId",
        value: cloudId,
        type: "input",
        disabled: true,
      },
    ];
    viewData.value = [...viewData.value, ...arr];
  } else if (type == "delete") {
    // 删除
    flag.value = {};
    Modal.confirm({
      title: locales.value.tishi,
      content: locales.value.shanchuqueren,
      centered: true,
      onOk: async () => {
        let res = await deleteRelay({
          id: tableData.selectedRowKeys.join(","),
        });
        if (res.data.code === 0) {
          message.success(locales.value.caozuochenggong);
          handleQuery();
        } else {
          message.warning(locales.value.caozuoshibai);
        }
      },
    });
  } else if (type == "select-component") {
    // 选择组件
    flag.value = { label: locales.value.xuanzezujian, value: "deep" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    curApi.value = relayQueryComponent;
    params.value = {
      relayId: curData.relayId || "",
      operationFlag: "select",
    };
    deepColumn.value = viewComponentColumn;
    btns.value = [
      { label: locales.value.queren, value: "confirm" },
      { label: locales.value.fanhui, value: "back" },
    ];
    searchDeepData.value = [
      {
        label: locales.value.zujianzuobiao,
        key: "chipId",
        value: undefined,
        type: "input",
      },
      {
        label: locales.value.zuchuanname,
        key: "groupName",
        value: undefined,
        type: "input",
      },
    ];
  } else if (type == "view-component") {
    // 查看组件
    flag.value = { label: locales.value.chakanzujian, value: "deep" };
    let curData = tableData.dataSource.find(
      (e) => e.id == tableData.selectedRowKeys[0]
    );
    curApi.value = relayQueryComponent;
    params.value = {
      relayId: curData.relayId || "",
      operationFlag: "view",
    };
    deepColumn.value = viewComponentColumn;
    btns.value = [
      { label: locales.value.yichu, value: "remove" },
      { label: locales.value.fanhui, value: "back" },
    ];
    searchDeepData.value = [
      {
        label: locales.value.zujianzuobiao,
        key: "chipId",
        value: undefined,
        type: "input",
      },
      {
        label: locales.value.zuchuanname,
        key: "groupName",
        value: undefined,
        type: "input",
      },
    ];
  } else if (type == "view-version") {
    // 查询版本
    openVersion.value = true;
    flag.value = {};
  }
}

let btns = ref([]);
let curApi = ref(null);
let params = ref({});
let deepColumn = ref([]);
let refresh = ref(false);
let searchDeepData = ref([]);
function handleBtnClick({ type, value }) {
  if (refresh.value) refresh.value = false;
  if (type == "back") flag.value = {};
  else {
    if (value?.length) {
      Modal.confirm({
        title: locales.value.tishi,
        content: locales.value.caozuoqueren,
        centered: true,
        onOk: async () => {
          let res = null;
          if (type == "confirm") {
            res = await relayChangeComponent({
              relayId: tableData.selectedRowKeys[0],
              id: value.join(","),
            });
          } else if (type == "remove") {
            res = await relayChangeComponent({
              id: value.join(","),
            });
          }
          if (res.data.code === 0) {
            message.success(locales.value.caozuochenggong);
            refresh.value = true;
            searchDeepData.value = [
              {
                label: locales.value.zujianzuobiao,
                key: "chipId",
                value: undefined,
                type: "input",
              },
              {
                label: locales.value.zuchuanname,
                key: "groupName",
                value: undefined,
                type: "input",
              },
            ];
          } else {
            message.warning(locales.value.caozuoshibai);
          }
        },
      });
    } else message.info(locales.value.zhengquexuanzecaozuoxiang);
  }
}

async function handleVersionQuery(queryType) {
  let res = await versionQueryTask({
    queryType,
    id: tableData.selectedRowKeys[0],
  });
  if (res?.data?.code === 0) {
    message.success(locales.value.caozuochenggong);
    handleQuery();
  } else message.warning(locales.value.caozuoshibai);
  openVersion.value = false;
}
</script>

<style lang="less" scoped>
.repeater {
  .btns {
    margin-bottom: 12px;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;

    .action-btn {
      cursor: pointer;
      font-size: 14px;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &.action-btn-primary {
        color: #1890ff;

        &:hover {
          color: #40a9ff;
          background-color: #e6f7ff;
        }
      }

      &.action-btn-danger {
        color: #ff4d4f;

        &:hover {
          color: #ff7875;
          background-color: #fff2f0;
        }
      }
    }
  }
}
</style>

<style lang="less">
.relay-modal {
  width: 450px !important;
  .modal-box {
    padding: 20px;
    .btn {
      width: 140px !important;
      border-radius: 2px !important;
    }
  }
  .ant-modal-footer {
    .ant-btn-primary {
      display: none !important;
    }
  }
}
</style>